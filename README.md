# Ansible 自动化运维平台

本仓库包含一个基于 Ansible 的自动化平台，用于基础设施管理和性能测试。该平台支持多个项目环境，包括医疗后端运维和性能测试场景。

## 功能特性

- **多项目架构**: 为不同运维领域提供有组织的结构
- **基础设施自动化**: 完整的服务器设置和配置管理
- **性能测试框架**: 集成基于 Locust 的负载测试功能
- **安全集成**: 支持 Vault 加密敏感数据
- **Git 钩子集成**: 自动化验证和质量检查，包含 .pem 文件权限管理
- **容器集成**: 基于 Docker 的部署支持，配备 Semaphore UI
- **基础设施即代码**: Terraform 集成用于云资源管理
- **质量保证**: 全面的验证和测试框架

## 平台架构

```
ansible/
├── .githooks/                               # 自动化 Git 钩子
│   ├── post-checkout                       # 自动设置 .pem 文件权限
│   └── post-merge                          # 自动设置 .pem 文件权限
├── env/                                     # 环境配置
│   ├── Binary.md                           # Semaphore 二进制安装指南
│   ├── check_semaphore.sh                  # Semaphore 健康检查脚本
│   ├── docker-compose.yml                  # Docker 部署配置
│   └── semaphore.sh                        # Semaphore 管理脚本
├── projects/                               # 独立项目模块
│   ├── ansible.demo.cfg                    # 演示 Ansible 配置
│   ├── medical-backend/                    # 医疗后端运维
│   │   ├── roles/                          # 医疗专用角色
│   │   ├── inventory/                      # 医疗基础设施清单
│   │   ├── extra-vars/                     # 变量配置
│   │   ├── site.yml                       # 医疗部署剧本
│   │   ├── README.md                      # 医疗后端文档
│   │   └──  USAGE.md                       # 详细使用说明
│   └── performance/                       # 性能测试框架
│       ├── roles/                          # 性能测试角色
│       ├── extra-vars/                     # 测试场景配置
│       ├── inventory/                      # 性能测试清单
│       ├── site.yml                       # 性能测试剧本
│       └── README.md                      # 性能测试文档
├── terraform/                             # 基础设施即代码
│   ├── README.md                          # Terraform 文档
│   └── aws/                               # AWS 专用配置
│       └── windows/                       # Windows EC2 管理
├── test_gathering.yml                     # Ansible 事实收集测试
├── vault-vars.sh                          # Vault 加密工具
└── README.md                              # 本文档
```

## 先决条件

### 系统要求

- Ubuntu/Debian 基础的 Linux 系统
- Python 3.6 或更高版本
- 正确配置的 Git
- Docker 和 Docker Compose（用于 Semaphore UI）
- 对目标服务器的 SSH 访问权限

### 必需工具

1. **Ansible**: 基础设施自动化引擎
2. **Semaphore**: 基于 Web 的 Ansible UI（可选但推荐）
3. **Git**: 版本控制，集成钩子功能
4. **Docker**: 用于 Semaphore 部署的容器运行时
5. **Terraform**: 云资源管理的基础设施即代码工具（可选）

## 安装

### 安装 Ansible

```bash
# 添加 Ansible 仓库
sudo apt-add-repository ppa:ansible/ansible
sudo apt update

# 安装 Ansible
sudo apt install ansible

# 升级 Ansible（需要时）
sudo apt update
sudo apt upgrade ansible

# 验证安装
ansible --version
```

### 安装 Semaphore UI

Semaphore 提供基于 Web 的界面来管理 Ansible 操作。虽然平台支持完整的 Docker 部署，但推荐使用混合方法以获得更好的性能和资源管理。

#### 推荐的安装方法（混合）

为了获得最佳性能，建议仅对数据库层使用容器，并将 Semaphore 作为二进制文件运行：

1. **仅对数据库使用 Docker Compose**: 使用提供的 docker-compose 配置部署 PostgreSQL 数据库
2. **将 Semaphore 作为二进制文件运行**: 使用原生 Semaphore 二进制文件作为 Web 界面和执行引擎

**混合方法的优势：**
- 更好的性能和资源利用率
- 更容易的维护和更新
- 更灵活的配置选项
- 减少容器开销

👉 **[二进制安装指南](env/Binary.md)** - 二进制部署的完整设置说明

#### 完整 Docker 部署（替代方案）

如果您更喜欢完全容器化的设置，可以使用完整的 Docker Compose 配置：

```bash
# 创建自定义 docker 网络
sudo docker network create ${YOUR_NETWORK_NAME}

# 验证配置
sudo docker-compose config
# 或使用环境文件
sudo docker-compose --env-file .env config

# 启动 Semaphore（完整堆栈）
sudo docker-compose up -d
```

配置详情请参见：[docker-compose.yml](env/docker-compose.yml)

#### 仅数据库 Docker 部署（推荐）

对于混合方法，仅启动数据库服务：

```bash
# 创建自定义 docker 网络
sudo docker network create ${YOUR_NETWORK_NAME}

# 仅启动 PostgreSQL 数据库
sudo docker-compose up -d semaphore_db

# 验证数据库正在运行
sudo docker-compose ps semaphore_db
```

然后按照[二进制安装指南](env/Binary.md)使用容器化数据库设置 Semaphore 二进制文件。

## 配置

### Git 配置设置

确保 Git 钩子和文件权限的正确配置：

```bash
# 检查并设置 git filemode
git config --get core.filemode
# 如果不是 true，则设置它
git config core.filemode true

# 检查并设置 git hooks 路径
git config --get core.hooksPath
# 如果不是 .githooks，则设置它
git config core.hooksPath .githooks
```

**Git 钩子功能：**
- **自动 .pem 文件权限管理**: Post-checkout 和 post-merge 钩子自动为 SSH 密钥文件设置正确权限（600）
- **安全增强**: 确保 SSH 密钥在 Git 操作后得到适当保护
- **无缝集成**: 与正常的 Git 工作流程透明地工作

### Vault 配置

对于使用加密变量的项目，根据需要配置 vault 密码文件。可以使用 vault 加密工具 `vault-vars.sh` 来加密敏感配置文件。

## 使用方法

### 通用命令结构

> 📌 **重要提示：**
> 以下命令中的 `--vault-password-file VPF` 参数仅在使用 `vault-vars.sh` 加密了 `projects/<project_dir_name>/roles/*/vars/main.yml` 文件时才需要。否则，可以省略此参数！

### 发现命令

使用这些命令来探索和了解可用资源：

```bash
# 列出项目的所有目标主机
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-hosts

# 列出所有可用标签
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-tags

# 列出将要执行的所有任务
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF --list-tasks
```

### Execution Commands

Execute playbooks with full or selective task execution:

```bash
# Execute all tasks in a project
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF

# Execute specific tasks using tags
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -t <tag-name>

# Execute with extra variables
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -e "variable=value"

# Execute with verbose output for debugging
ansible-playbook projects/<project_dir_name>/site.yml --vault-password-file VPF -vvv
```

## 可用项目

### 医疗后端运维

医疗后端系统的完整基础设施自动化，包括：
- 服务器配置和部署
- 安全加固和合规性
- 应用程序部署自动化
- 健康监控和维护脚本
- 具有错误处理的强大脚本执行框架
- 全面的日志记录和备份管理

👉 **[医疗后端文档](projects/medical-backend/README.md)**
👉 **[详细使用指南](projects/medical-backend/USAGE.md)**
👉 **[验证报告](projects/medical-backend/VALIDATION_REPORT.md)**

#### 快速开始示例

```bash
# 在 Web 服务器上执行健康检查脚本
ansible-playbook projects/medical-backend/site.yml -e "script_name=health_check.sh target_group=web_servers"

# 部署后端服务
ansible-playbook projects/medical-backend/site.yml -t deploy_backend

# 运行维护脚本
ansible-playbook projects/medical-backend/site.yml -e "script_name=maintenance.sh target_group=all"

# 使用额外变量文件执行
ansible-playbook projects/medical-backend/site.yml -t execute_shell -e "@extra-vars/kenta.backend.yml"
```

### 性能测试框架

使用 Locust 的综合负载测试基础设施：
- Automated test environment deployment
- Distributed load testing capabilities
- Performance metrics collection and analysis
- Multiple testing scenario support
- Conda environment management for testing tools
- Scalable architecture with generator nodes

👉 **[Performance Testing Documentation](projects/performance/README.md)**

#### Quick Start Examples

```bash
# Deploy complete performance testing environment
ansible-playbook projects/performance/site.yml -t deploy_all

# Run load test with enquete scenario
ansible-playbook projects/performance/site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# Run load test with presentation scenario
ansible-playbook projects/performance/site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-pres.yml"

# Prepare infrastructure only
ansible-playbook projects/performance/site.yml -t play_prepare
```

### Infrastructure as Code (Terraform)

Terraform configurations for cloud resource management:
- AWS EC2 instance state management (start/stop)
- Modular and reusable infrastructure components
- Support for multiple cloud environments
- Integration with existing Ansible workflows

👉 **[Terraform Documentation](terraform/README.md)**

#### Quick Start Examples

```bash
# Navigate to terraform directory
cd terraform/aws/windows

# Initialize Terraform
terraform init

# Start an EC2 instance
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=start"

# Stop an EC2 instance
terraform apply -var="region=us-east-1" -var="instance_id=i-1234567890abcdef0" -var="action=stop"
```

### Testing and Validation

Built-in testing capabilities for ensuring platform reliability:
- Ansible facts gathering behavior testing
- Syntax validation and linting
- Project-specific validation reports
- Automated quality checks via Git hooks

#### Testing Examples

```bash
# Test Ansible facts gathering behavior
ansible-playbook test_gathering.yml

# Validate project syntax
ansible-playbook projects/medical-backend/site.yml --syntax-check

# Check inventory configuration
ansible-inventory --list -i projects/medical-backend/inventory/
```

## 项目开发指南

### Adding New Projects

When creating new projects:

1. **Follow the established directory structure**
2. **Create comprehensive documentation** in project README
3. **Implement proper role dependencies**
4. **Include example configurations** and usage scenarios
5. **Test thoroughly** in development environments

### Role Development Standards

- Use descriptive role names with numbered prefixes (e.g., `01-prepare`, `02-deploy`)
- Implement idempotent operations
- Include proper error handling and validation
- Document all variables and their purposes
- Follow Ansible best practices for security

### Variable Management

- Use consistent naming conventions across projects
- Leverage vault encryption for sensitive data
- Organize variables by scope (global, role-specific, environment-specific)
- Provide default values where appropriate

## 安全最佳实践

### Access Control

- Use SSH keys for authentication instead of passwords
- Implement least-privilege access principles
- Regularly rotate SSH keys and vault passwords
- Monitor and log all automation activities

### Data Protection

- Encrypt sensitive variables using Ansible Vault
- Avoid storing credentials in plain text
- Use secure networks for automation traffic
- Implement proper backup and recovery procedures

### Network Security

- Use VPNs or private networks for management traffic
- Implement firewall rules for Ansible communications
- Monitor network traffic for anomalies
- Secure Semaphore UI with proper authentication

## 监控和日志

### Execution Logging

- Ansible execution logs are stored in project directories
- Semaphore provides web-based execution history
- Use verbose modes for detailed troubleshooting
- Monitor system resources during large deployments

### Health Monitoring

- Implement regular health checks using automated scripts
- Monitor target system performance and availability
- Set up alerting for critical infrastructure components
- Maintain deployment and change logs

## 故障排除

### Common Issues

1. **SSH Connection Problems**
   - Verify SSH keys and permissions
   - Check network connectivity to target hosts
   - Validate inventory configuration

2. **Vault Decryption Errors**
   - Ensure correct vault password file
   - Verify vault file encryption status
   - Check file permissions on vault files

3. **Role Dependency Issues**
   - Verify role paths and dependencies
   - Check for circular dependencies
   - Validate role metadata configuration

4. **Performance Issues**
   - Monitor system resources during execution
   - Optimize playbook parallelization settings
   - Review network latency to target hosts

### Debug Mode

Enable verbose output for detailed troubleshooting:

```bash
# Basic verbose output
ansible-playbook projects/<project>/site.yml -v

# Detailed debug information
ansible-playbook projects/<project>/site.yml -vvv

# Connection debugging
ansible-playbook projects/<project>/site.yml -vvvv
```

### Validation Commands

```bash
# Test connectivity to all hosts
ansible all -m ping -i projects/<project>/inventory/

# Check Ansible configuration
ansible-config dump

# Validate playbook syntax
ansible-playbook projects/<project>/site.yml --syntax-check

# Perform dry run without making changes
ansible-playbook projects/<project>/site.yml --check
```

## 贡献

### Development Workflow

1. **Create feature branches** for new development
2. **Follow coding standards** and documentation requirements
3. **Test thoroughly** in development environments
4. **Update documentation** for any changes
5. **Submit pull requests** with clear descriptions

### Code Quality

- Use Git hooks for automated validation
- Follow Ansible best practices and conventions
- Implement comprehensive error handling
- Include appropriate comments and documentation
- Test with different operating system versions

### Documentation Standards

- Keep README files updated with any changes
- Document all variables and their purposes
- Provide clear usage examples
- Include troubleshooting information
- Maintain version compatibility notes

## 支持和资源

### Internal Resources

- Project-specific documentation in individual README files
- Git hooks for automated validation and quality checks
- Semaphore UI for web-based management and monitoring

### External Resources

- [Ansible Documentation](https://docs.ansible.com/)
- [Ansible Best Practices](https://docs.ansible.com/ansible/latest/user_guide/playbooks_best_practices.html)
- [Semaphore Documentation](https://docs.semui.co/)
- [Docker Documentation](https://docs.docker.com/)
- [Terraform Documentation](https://www.terraform.io/docs/)
- [Locust Documentation](https://docs.locust.io/)

### Getting Help

1. **Check project-specific documentation** first
2. **Review troubleshooting sections** for common issues
3. **Use debug modes** for detailed error information
4. **Validate configurations** using provided commands
5. **Monitor system logs** for infrastructure-level issues