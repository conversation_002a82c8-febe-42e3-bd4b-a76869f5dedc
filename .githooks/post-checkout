#!/bin/bash
echo "Post-checkout hook is running..."
echo "Current directory: $(pwd)"

PEM_FILES=($(find . -type f -name "*.pem"))

if [ ${#PEM_FILES[@]} -eq 0 ]; then
    echo "No .pem files found in repository"
    exit 0
fi

for PEM_FILE in "${PEM_FILES[@]}"; do
    if [ -f "$PEM_FILE" ]; then
        chmod 600 "$PEM_FILE"
        echo "Set permissions of $PEM_FILE to 600"
    else
        echo "Warning: $PEM_FILE not found"
    fi
done

echo "Post-checkout hook completed."