[defaults]
# (boolean) By default, Ansible will issue a warning when received from a task action (module or action plugin).
# These warnings can be silenced by adjusting this setting to False.
;action_warnings=True

# (list) Accept a list of cowsay templates that are 'safe' to use, set to an empty list if you want to enable all installed templates.
;cowsay_enabled_stencils=bud-frogs, bunny, cheese, daemon, default, dragon, elephant-in-snake, elephant, eyes, hellokitty, kitty, luke-koala, meow, milk, moofasa, moose, ren, sheep, small, stegosaurus, stimpy, supermilker, three-eyes, turkey, turtle, tux, udder, vader-koala, vader, www

# (string) Specify a custom cowsay path or swap in your cowsay implementation of choice.
;cowpath=

# (string) This allows you to choose a specific cowsay stencil for the banners or use 'random' to cycle through them.
;cow_selection=default

# (boolean) This option forces color mode even when running without a TTY or the "nocolor" setting is True.
;force_color=False

# (path) The default root path for Ansible config files on the controller.
;home=~/.ansible

# (boolean) This setting allows suppressing colorizing output, which is used to give a better indication of failure and status information.
;nocolor=False

# (boolean) If you have cowsay installed but want to avoid the 'cows' (why????), use this.
;nocows=False

# (boolean) Sets the default value for the any_errors_fatal keyword, if True, Task failures will be considered fatal errors.
;any_errors_fatal=False

# (path) The password file to use for the become plugin. ``--become-password-file``.
# If executable, it will be run and the resulting stdout will be used as the password.
;become_password_file=

# (pathspec) Colon-separated paths in which Ansible will search for Become Plugins.
;become_plugins=/home/<USER>/.ansible/plugins/become:/usr/share/ansible/plugins/become

# (string) Chooses which cache plugin to use, the default 'memory' is ephemeral.
;fact_caching=memory

# (string) Defines connection or path information for the cache plugin.
;fact_caching_connection=

# (string) Prefix to use for cache plugin files/tables.
;fact_caching_prefix=ansible_facts

# (integer) Expiration timeout for the cache plugin data.
;fact_caching_timeout=86400

# (list) List of enabled callbacks, not all callbacks need enabling, but many of those shipped with Ansible do as we don't want them activated by default.
;callbacks_enabled=

# (string) When a collection is loaded that does not support the running Ansible version (with the collection metadata key `requires_ansible`).
;collections_on_ansible_version_mismatch=warning

# (pathspec) Colon-separated paths in which Ansible will search for collections content. Collections must be in nested *subdirectories*, not directly in these directories. For example, if ``COLLECTIONS_PATHS`` includes ``'{{ ANSIBLE_HOME ~ "/collections" }}'``, and you want to add ``my.collection`` to that directory, it must be saved as ``'{{ ANSIBLE_HOME} ~ "/collections/ansible_collections/my/collection" }}'``.

;collections_path=/home/<USER>/.ansible/collections:/usr/share/ansible/collections

# (boolean) A boolean to enable or disable scanning the sys.path for installed collections.
;collections_scan_sys_path=True

# (path) The password file to use for the connection plugin. ``--connection-password-file``.
;connection_password_file=

# (pathspec) Colon-separated paths in which Ansible will search for Action Plugins.
;action_plugins=/home/<USER>/.ansible/plugins/action:/usr/share/ansible/plugins/action

# (boolean) When enabled, this option allows lookup plugins (whether used in variables as ``{{lookup('foo')}}`` or as a loop as with_foo) to return data that is not marked 'unsafe'.
# By default, such data is marked as unsafe to prevent the templating engine from evaluating any jinja2 templating language, as this could represent a security risk. This option is provided to allow for backward compatibility, however, users should first consider adding allow_unsafe=True to any lookups that may be expected to contain data that may be run through the templating engine late.
;allow_unsafe_lookups=False

# (boolean) This controls whether an Ansible playbook should prompt for a login password. If using SSH keys for authentication, you probably do not need to change this setting.
;ask_pass=False

# (boolean) This controls whether an Ansible playbook should prompt for a vault password.
;ask_vault_pass=False

# (pathspec) Colon-separated paths in which Ansible will search for Cache Plugins.
;cache_plugins=/home/<USER>/.ansible/plugins/cache:/usr/share/ansible/plugins/cache

# (pathspec) Colon-separated paths in which Ansible will search for Callback Plugins.
;callback_plugins=/home/<USER>/.ansible/plugins/callback:/usr/share/ansible/plugins/callback

# (pathspec) Colon-separated paths in which Ansible will search for Cliconf Plugins.
;cliconf_plugins=/home/<USER>/.ansible/plugins/cliconf:/usr/share/ansible/plugins/cliconf

# (pathspec) Colon-separated paths in which Ansible will search for Connection Plugins.
;connection_plugins=/home/<USER>/.ansible/plugins/connection:/usr/share/ansible/plugins/connection

# (boolean) Toggles debug output in Ansible. This is *very* verbose and can hinder multiprocessing. Debug output can also include secret information despite no_log settings being enabled, which means debug mode should not be used in production.
;debug=False

# (string) This indicates the command to use to spawn a shell under, which is required for Ansible's execution needs on a target. Users may need to change this in rare instances when shell usage is constrained, but in most cases, it may be left as is.
;executable=/bin/sh

# (string) This option allows you to globally configure a custom path for 'local_facts' for the implied :ref:`ansible_collections.ansible.builtin.setup_module` task when using fact gathering.
# If not set, it will fall back to the default from the ``ansible.builtin.setup`` module: ``/etc/ansible/facts.d``.
# This does **not** affect user defined tasks that use the ``ansible.builtin.setup`` module.
# The real action being created by the implicit task is currently ``ansible.legacy.gather_facts`` module, which then calls the configured fact modules, by default this will be ``ansible.builtin.setup`` for POSIX systems but other platforms might have different defaults.
;fact_path=

# (pathspec) Colon-separated paths in which Ansible will search for Jinja2 Filter Plugins.
;filter_plugins=/home/<USER>/.ansible/plugins/filter:/usr/share/ansible/plugins/filter

# (boolean) This option controls if notified handlers run on a host even if a failure occurs on that host.
# When false, the handlers will not run if a failure has occurred on a host.
# This can also be set per play or on the command line. See Handlers and Failure for more details.
;force_handlers=False

# (integer) Maximum number of forks Ansible will use to execute tasks on target hosts.
;forks=5

# (string) This setting controls the default policy of fact gathering (facts discovered about remote systems).
# This option can be useful for those wishing to save fact gathering time. Both 'smart' and 'explicit' will use the cache plugin.
;gathering=implicit

# (list) Set the `gather_subset` option for the :ref:`ansible_collections.ansible.builtin.setup_module` task in the implicit fact gathering. See the module documentation for specifics.
# It does **not** apply to user defined ``ansible.builtin.setup`` tasks.
;gather_subset=

# (integer) Set the timeout in seconds for the implicit fact gathering, see the module documentation for specifics.
# It does **not** apply to user defined :ref:`ansible_collections.ansible.builtin.setup_module` tasks.
;gather_timeout=

# (string) This setting controls how duplicate definitions of dictionary variables (aka hash, map, associative array) are handled in Ansible.
# This does not affect variables whose values are scalars (integers, strings) or arrays.
# **WARNING**, changing this setting is not recommended as this is fragile and makes your content (plays, roles, collections) nonportable, leading to continual confusion and misuse. Don't change this setting unless you think you have an absolute need for it.
# We recommend avoiding reusing variable names and relying on the ``combine`` filter and ``vars`` and ``varnames`` lookups to create merged versions of the individual variables. In our experience, this is rarely needed and is a sign that too much complexity has been introduced into the data structures and plays.
# For some uses you can also look into custom vars_plugins to merge on input, even substituting the default ``host_group_vars`` that is in charge of parsing the ``host_vars/`` and ``group_vars/`` directories. Most users of this setting are only interested in inventory scope, but the setting itself affects all sources and makes debugging even harder.
# All playbooks and roles in the official examples repos assume the default for this setting.
# Changing the setting to ``merge`` applies across variable sources, but many sources will internally still overwrite the variables. For example ``include_vars`` will dedupe variables internally before updating Ansible, with 'last defined' overwriting previous definitions in same file.
# The Ansible project recommends you **avoid ``merge`` for new projects.**
# It is the intention of the Ansible developers to eventually deprecate and remove this setting, but it is being kept as some users do heavily rely on it. New projects should **avoid 'merge'**.
;hash_behaviour=replace

# (pathlist) Comma-separated list of Ansible inventory sources
;inventory=/etc/ansible/hosts

# (pathspec) Colon-separated paths in which Ansible will search for HttpApi Plugins.
;httpapi_plugins=/home/<USER>/.ansible/plugins/httpapi:/usr/share/ansible/plugins/httpapi

# (float) This sets the interval (in seconds) of Ansible internal processes polling each other. Lower values improve performance with large playbooks at the expense of extra CPU load. Higher values are more suitable for Ansible usage in automation scenarios when UI responsiveness is not required but CPU usage might be a concern.
# The default corresponds to the value hardcoded in Ansible <= 2.1
;internal_poll_interval=0.001

# (pathspec) Colon-separated paths in which Ansible will search for Inventory Plugins.
;inventory_plugins=/home/<USER>/.ansible/plugins/inventory:/usr/share/ansible/plugins/inventory

# (string) This is a developer-specific feature that allows enabling additional Jinja2 extensions.
# See the Jinja2 documentation for details. If you do not know what these do, you probably don't need to change this setting :)
;jinja2_extensions=[]

# (boolean) This option preserves variable types during template operations.
;jinja2_native=False

# (boolean) Enables/disables the cleaning up of the temporary files Ansible used to execute the tasks on the remote.
# If this option is enabled it will disable ``ANSIBLE_PIPELINING``.
;keep_remote_files=False

# (boolean) Controls whether callback plugins are loaded when running /usr/bin/ansible. This may be used to log activity from the command line, send notifications, and so on. Callback plugins are always loaded for ``ansible-playbook``.
;bin_ansible_callbacks=False

# (tmppath) Temporary directory for Ansible to use on the controller.
;local_tmp=/home/<USER>/.ansible/tmp

# (list) List of logger names to filter out of the log file.
;log_filter=

# (path) File to which Ansible will log on the controller. When empty logging is disabled.
;log_path=

# (pathspec) Colon-separated paths in which Ansible will search for Lookup Plugins.
;lookup_plugins=/home/<USER>/.ansible/plugins/lookup:/usr/share/ansible/plugins/lookup

# (string) Sets the macro for the 'ansible_managed' variable available for :ref:`ansible_collections.ansible.builtin.template_module` and :ref:`ansible_collections.ansible.windows.win_template_module`.  This is only relevant to those two modules.
;ansible_managed=Ansible managed

# (string) This sets the default arguments to pass to the ``ansible`` adhoc binary if no ``-a`` is specified.
;module_args=

# (string) Compression scheme to use when transferring Python modules to the target.
;module_compression=ZIP_DEFLATED

# (string) Module to use with the ``ansible`` AdHoc command, if none is specified via ``-m``.
;module_name=command

# (pathspec) Colon-separated paths in which Ansible will search for Modules.
;library=/home/<USER>/.ansible/plugins/modules:/usr/share/ansible/plugins/modules

# (pathspec) Colon-separated paths in which Ansible will search for Module utils files, which are shared by modules.
;module_utils=/home/<USER>/.ansible/plugins/module_utils:/usr/share/ansible/plugins/module_utils

# (pathspec) Colon-separated paths in which Ansible will search for Netconf Plugins.
;netconf_plugins=/home/<USER>/.ansible/plugins/netconf:/usr/share/ansible/plugins/netconf

# (boolean) Toggle Ansible's display and logging of task details, mainly used to avoid security disclosures.
;no_log=False

# (boolean) Toggle Ansible logging to syslog on the target when it executes tasks. On Windows hosts, this will disable a newer style PowerShell modules from writing to the event log.
;no_target_syslog=False

# (raw) What templating should return as a 'null' value. When not set it will let Jinja2 decide.
;null_representation=

# (integer) For asynchronous tasks in Ansible (covered in Asynchronous Actions and Polling), this is how often to check back on the status of those tasks when an explicit poll interval is not supplied. The default is a reasonably moderate 15 seconds which is a tradeoff between checking in frequently and providing a quick turnaround when something may have completed.
;poll_interval=15

# (path) Option for connections using a certificate or key file to authenticate, rather than an agent or passwords, you can set the default value here to avoid re-specifying ``--private-key`` with every invocation.
;private_key_file=

# (boolean) By default, imported roles publish their variables to the play and other roles, this setting can avoid that.
# This was introduced as a way to reset role variables to default values if a role is used more than once in a playbook.
# Starting in version '2.17' M(ansible.builtin.include_roles) and M(ansible.builtin.import_roles) can individually override this via the C(public) parameter.
# Included roles only make their variables public at execution, unlike imported roles which happen at playbook compile time.
;private_role_vars=False

# (integer) Port to use in remote connections, when blank it will use the connection plugin default.
;remote_port=

# (string) Sets the login user for the target machines
# When blank it uses the connection plugin's default, normally the user currently executing Ansible.
;remote_user=

# (pathspec) Colon-separated paths in which Ansible will search for Roles.
;roles_path=/home/<USER>/.ansible/roles:/usr/share/ansible/roles:/etc/ansible/roles

# (string) Set the main callback used to display Ansible output. You can only have one at a time.
# You can have many other callbacks, but just one can be in charge of stdout.
# See :ref:`callback_plugins` for a list of available options.
;stdout_callback=default

# (string) Set the default strategy used for plays.
;strategy=linear

# (pathspec) Colon-separated paths in which Ansible will search for Strategy Plugins.
;strategy_plugins=/home/<USER>/.ansible/plugins/strategy:/usr/share/ansible/plugins/strategy

# (boolean) Toggle the use of "su" for tasks.
;su=False

# (string) Syslog facility to use when Ansible logs to the remote target.
;syslog_facility=LOG_USER

# (pathspec) Colon-separated paths in which Ansible will search for Terminal Plugins.
;terminal_plugins=/home/<USER>/.ansible/plugins/terminal:/usr/share/ansible/plugins/terminal

# (pathspec) Colon-separated paths in which Ansible will search for Jinja2 Test Plugins.
;test_plugins=/home/<USER>/.ansible/plugins/test:/usr/share/ansible/plugins/test

# (integer) This is the default timeout for connection plugins to use.
;timeout=10

# (string) Can be any connection plugin available to your ansible installation.
# There is also a (DEPRECATED) special 'smart' option, that will toggle between 'ssh' and 'paramiko' depending on controller OS and ssh versions.
;transport=ssh

# (boolean) When True, this causes ansible templating to fail steps that reference variable names that are likely typoed.
# Otherwise, any '{{ template_expression }}' that contains undefined variables will be rendered in a template or ansible action line exactly as written.
;error_on_undefined_vars=True

# (pathspec) Colon-separated paths in which Ansible will search for Vars Plugins.
;vars_plugins=/home/<USER>/.ansible/plugins/vars:/usr/share/ansible/plugins/vars

# (string) The vault_id to use for encrypting by default. If multiple vault_ids are provided, this specifies which to use for encryption. The ``--encrypt-vault-id`` CLI option overrides the configured value.
;vault_encrypt_identity=

# (string) The label to use for the default vault id label in cases where a vault id label is not provided.
;vault_identity=default

# (list) A list of vault-ids to use by default. Equivalent to multiple ``--vault-id`` args. Vault-ids are tried in order.
;vault_identity_list=

# (string) If true, decrypting vaults with a vault id will only try the password from the matching vault-id.
;vault_id_match=False

# (path) The vault password file to use. Equivalent to ``--vault-password-file`` or ``--vault-id``.
# If executable, it will be run and the resulting stdout will be used as the password.
;vault_password_file=

# (integer) Sets the default verbosity, equivalent to the number of ``-v`` passed in the command line.
;verbosity=0

# (boolean) Toggle to control the showing of deprecation warnings
;deprecation_warnings=True

# (boolean) Toggle to control showing warnings related to running devel.
;devel_warning=True

# (boolean) Normally ``ansible-playbook`` will print a header for each task that is run. These headers will contain the name: field from the task if you specified one. If you didn't then ``ansible-playbook`` uses the task's action to help you tell which task is presently running. Sometimes you run many of the same action and so you want more information about the task to differentiate it from others of the same action. If you set this variable to True in the config then ``ansible-playbook`` will also include the task's arguments in the header.
# This setting defaults to False because there is a chance that you have sensitive values in your parameters and you do not want those to be printed.
# If you set this to True you should be sure that you have secured your environment's stdout (no one can shoulder surf your screen and you aren't saving stdout to an insecure file) or made sure that all of your playbooks explicitly added the ``no_log: True`` parameter to tasks that have sensitive values :ref:`keep_secret_data` for more information.
;display_args_to_stdout=False

# (boolean) Toggle to control displaying skipped task/host entries in a task in the default callback.
;display_skipped_hosts=True

# (string) Root docsite URL used to generate docs URLs in warning/error text; must be an absolute URL with a valid scheme and trailing slash.
;docsite_root_url=https://docs.ansible.com/ansible-core/

# (pathspec) Colon-separated paths in which Ansible will search for Documentation Fragments Plugins.
;doc_fragment_plugins=/home/<USER>/.ansible/plugins/doc_fragments:/usr/share/ansible/plugins/doc_fragments

# (string) By default, Ansible will issue a warning when a duplicate dict key is encountered in YAML.
# These warnings can be silenced by adjusting this setting to False.
;duplicate_dict_key=warn

# (boolean) Whether or not to enable the task debugger, this previously was done as a strategy plugin.
# Now all strategy plugins can inherit this behavior. The debugger defaults to activating when
# a task is failed on unreachable. Use the debugger keyword for more flexibility.
;enable_task_debugger=False

# (boolean) Toggle to allow missing handlers to become a warning instead of an error when notifying.
;error_on_missing_handler=True

# (list) Which modules to run during a play's fact gathering stage, using the default of 'smart' will try to figure it out based on connection type.
# If adding your own modules but you still want to use the default Ansible facts, you will want to include 'setup' or corresponding network module to the list (if you add 'smart', Ansible will also figure it out).
# This does not affect explicit calls to the 'setup' module, but does always affect the 'gather_facts' action (implicit or explicit).
;facts_modules=smart

# (boolean) Set this to "False" if you want to avoid host key checking by the underlying connection plugin Ansible uses to connect to the host.
# Please read the documentation of the specific connection plugin used for details.
;host_key_checking=True

# (boolean) Facts are available inside the `ansible_facts` variable, this setting also pushes them as their own vars in the main namespace.
# Unlike inside the `ansible_facts` dictionary where the prefix `ansible_` is removed from fact names, these will have the exact names that are returned by the module.
;inject_facts_as_vars=True

# (string) Path to the Python interpreter to be used for module execution on remote targets, or an automatic discovery mode. Supported discovery modes are ``auto`` (the default), ``auto_silent``, ``auto_legacy``, and ``auto_legacy_silent``. All discovery modes employ a lookup table to use the included system Python (on distributions known to include one), falling back to a fixed ordered list of well-known Python interpreter locations if a platform-specific default is not available. The fallback behavior will issue a warning that the interpreter should be set explicitly (since interpreters installed later may change which one is used). This warning behavior can be disabled by setting ``auto_silent`` or ``auto_legacy_silent``. The value of ``auto_legacy`` provides all the same behavior, but for backward-compatibility with older Ansible releases that always defaulted to ``/usr/bin/python``, will use that interpreter if present.
;interpreter_python=auto

# (boolean) If 'false', invalid attributes for a task will result in warnings instead of errors.
;invalid_task_attribute_failed=True

# (boolean) By default, Ansible will issue a warning when there are no hosts in the inventory.
# These warnings can be silenced by adjusting this setting to False.
;localhost_warning=True

# (int) This will set log verbosity if higher than the normal display verbosity, otherwise it will match that.
;log_verbosity=

# (int) Maximum size of files to be considered for diff display.
;max_diff_size=104448

# (list) List of extensions to ignore when looking for modules to load.
# This is for rejecting script and binary module fallback extensions.
;module_ignore_exts=.pyc, .pyo, .swp, .bak, ~, .rpm, .md, .txt, .rst, .yaml, .yml, .ini

# (bool) Enables whether module responses are evaluated for containing non-UTF-8 data.
# Disabling this may result in unexpected behavior.
# Only ansible-core should evaluate this configuration.
;module_strict_utf8_response=True

# (list) TODO: write it
;network_group_modules=eos, nxos, ios, iosxr, junos, enos, ce, vyos, sros, dellos9, dellos10, dellos6, asa, aruba, aireos, bigip, ironware, onyx, netconf, exos, voss, slxos

# (boolean) Previously Ansible would only clear some of the plugin loading caches when loading new roles, this led to some behaviors in which a plugin loaded in previous plays would be unexpectedly 'sticky'. This setting allows the user to return to that behavior.
;old_plugin_cache_clear=False

# (path) A number of non-playbook CLIs have a ``--playbook-dir`` argument; this sets the default value for it.
;playbook_dir=

# (string) This sets which playbook dirs will be used as a root to process vars plugins, which includes finding host_vars/group_vars.
;playbook_vars_root=top

# (path) A path to configuration for filtering which plugins installed on the system are allowed to be used.
# See :ref:`plugin_filtering_config` for details of the filter file's format.
#  The default is /etc/ansible/plugin_filters.yml
;plugin_filters_cfg=

# (string) Attempts to set RLIMIT_NOFILE soft limit to the specified value when executing Python modules (can speed up subprocess usage on Python 2.x. See https://bugs.python.org/issue11284). The value will be limited by the existing hard limit. Default value of 0 does not attempt to adjust existing system-defined limits.
;python_module_rlimit_nofile=0

# (bool) This controls whether a failed Ansible playbook should create a .retry file.
;retry_files_enabled=False

# (path) This sets the path in which Ansible will save .retry files when a playbook fails and retry files are enabled.
# This file will be overwritten after each run with the list of failed hosts from all plays.
;retry_files_save_path=

# (str) This setting can be used to optimize vars_plugin usage depending on the user's inventory size and play selection.
;run_vars_plugins=demand

# (bool) This adds the custom stats set via the set_stats plugin to the default output.
;show_custom_stats=False

# (string) Action to take when a module parameter value is converted to a string (this does not affect variables). For string parameters, values such as '1.00', "['a', 'b',]", and 'yes', 'y', etc. will be converted by the YAML parser unless fully quoted.
# Valid options are 'error', 'warn', and 'ignore'.
# Since 2.8, this option defaults to 'warn' but will change to 'error' in 2.12.
;string_conversion_action=warn

# (boolean) Allows disabling of warnings related to potential issues on the system running Ansible itself (not on the managed hosts).
# These may include warnings about third-party packages or other conditions that should be resolved if possible.
;system_warnings=True

# (string) A string to insert into target logging for tracking purposes
;target_log_info=

# (boolean) This option defines whether the task debugger will be invoked on a failed task when ignore_errors=True is specified.
# True specifies that the debugger will honor ignore_errors, and False will not honor ignore_errors.
;task_debugger_ignore_errors=True

# (integer) Set the maximum time (in seconds) that a task can run for.
# If set to 0 (the default) there is no timeout.
;task_timeout=0

# (string) Make ansible transform invalid characters in group names supplied by inventory sources.
;force_valid_group_names=never

# (boolean) Toggles the use of persistence for connections.
;use_persistent_connections=False

# (bool) A toggle to disable validating a collection's 'metadata' entry for a module_defaults action group. Metadata containing unexpected fields or value types will produce a warning when this is True.
;validate_action_group_metadata=True

# (list) Accept list for variable plugins that require it.
;vars_plugins_enabled=host_group_vars

# (list) Allows to change the group variable precedence merge order.
;precedence=all_inventory, groups_inventory, all_plugins_inventory, all_plugins_play, groups_plugins_inventory, groups_plugins_play

# (string) The salt to use for the vault encryption. If it is not provided, a random salt will be used.
;vault_encrypt_salt=

# (bool) Force 'verbose' option to use stderr instead of stdout
;verbose_to_stderr=False

# (integer) For asynchronous tasks in Ansible (covered in Asynchronous Actions and Polling), this is how long, in seconds, to wait for the task spawned by Ansible to connect back to the named pipe used on Windows systems. The default is 5 seconds. This can be too low on slower systems, or systems under heavy load.
# This is not the total time an async command can run for, but is a separate timeout to wait for an async command to start. The task will only start to be timed against its async_timeout once it has connected to the pipe, so the overall maximum duration the task can take will be extended by the amount specified here.
;win_async_startup_timeout=5

# (list) Check all of these extensions when looking for 'variable' files which should be YAML or JSON or vaulted versions of these.
# This affects vars_files, include_vars, inventory and vars plugins among others.
;yaml_valid_extensions=.yml, .yaml, .json


[privilege_escalation]
# (boolean) Display an agnostic become prompt instead of displaying a prompt containing the command line supplied become method.
;agnostic_become_prompt=True

# (boolean) When ``False``(default), Ansible will skip using become if the remote user is the same as the become user, as this is normally a redundant operation. In other words root sudo to root.
# If ``True``, this forces Ansible to use the become plugin anyways as there are cases in which this is needed.
;become_allow_same_user=False

# (boolean) Toggles the use of privilege escalation, allowing you to 'become' another user after login.
;become=False

# (boolean) Toggle to prompt for privilege escalation password.
;become_ask_pass=False

# (string) executable to use for privilege escalation, otherwise Ansible will depend on PATH.
;become_exe=

# (string) Flags to pass to the privilege escalation executable.
;become_flags=

# (string) Privilege escalation method to use when `become` is enabled.
;become_method=sudo

# (string) The user your login/remote user 'becomes' when using privilege escalation, most systems will use 'root' when no user is specified.
;become_user=root


[persistent_connection]
# (path) Specify where to look for the ansible-connection script. This location will be checked before searching $PATH.
# If null, ansible will start with the same directory as the ansible script.
;ansible_connection_path=

# (int) This controls the amount of time to wait for a response from a remote device before timing out a persistent connection.
;command_timeout=30

# (integer) This controls the retry timeout for persistent connection to connect to the local domain socket.
;connect_retry_timeout=15

# (integer) This controls how long the persistent connection will remain idle before it is destroyed.
;connect_timeout=30

# (path) Path to the socket to be used by the connection persistence system.
;control_path_dir=/home/<USER>/.ansible/pc


[connection]
# (boolean) This is a global option, each connection plugin can override either by having more specific options or not supporting pipelining at all.
# Pipelining, if supported by the connection plugin, reduces the number of network operations required to execute a module on the remote server, by executing many Ansible modules without actual file transfer.
# It can result in a very significant performance improvement when enabled.
# However this conflicts with privilege escalation (become). For example, when using 'sudo:' operations you must first disable 'requiretty' in /etc/sudoers on all managed hosts, which is why it is disabled by default.
# This setting will be disabled if ``ANSIBLE_KEEP_REMOTE_FILES`` is enabled.
;pipelining=False


[colors]
# (string) Defines the color to use on 'Changed' task status.
;changed=yellow

# (string) Defines the default color to use for ansible-console.
;console_prompt=white

# (string) Defines the color to use when emitting debug messages.
;debug=dark gray

# (string) Defines the color to use when emitting deprecation messages.
;deprecate=purple

# (string) Defines the color to use when showing added lines in diffs.
;diff_add=green

# (string) Defines the color to use when showing diffs.
;diff_lines=cyan

# (string) Defines the color to use when showing removed lines in diffs.
;diff_remove=red

# (string) Defines the color to use when emitting error messages.
;error=red

# (string) Defines the color to use for highlighting.
;highlight=white

# (string) Defines the color to use when showing 'OK' task status.
;ok=green

# (string) Defines the color to use when showing 'Skipped' task status.
;skip=cyan

# (string) Defines the color to use on 'Unreachable' status.
;unreachable=bright red

# (string) Defines the color to use when emitting verbose messages. In other words, those that show with '-v's.
;verbose=blue

# (string) Defines the color to use when emitting warning messages.
;warn=bright purple


[selinux]
# (boolean) This setting causes libvirt to connect to LXC containers by passing ``--noseclabel`` parameter to ``virsh`` command. This is necessary when running on systems which do not have SELinux.
;libvirt_lxc_noseclabel=False

# (list) Some filesystems do not support safe operations and/or return inconsistent errors, this setting makes Ansible 'tolerate' those in the list without causing fatal errors.
# Data corruption may occur and writes are not always verified when a filesystem is in the list.
;special_context_filesystems=fuse, nfs, vboxsf, ramfs, 9p, vfat


[diff]
# (bool) Configuration toggle to tell modules to show differences when in 'changed' status, equivalent to ``--diff``.
;always=False

# (integer) Number of lines of context to show when displaying the differences between files.
;context=3


[galaxy]
# (path) The directory that stores cached responses from a Galaxy server.
# This is only used by the ``ansible-galaxy collection install`` and ``download`` commands.
# Cache files inside this dir will be ignored if they are world writable.
;cache_dir=/home/<USER>/.ansible/galaxy_cache

# (bool) whether ``ansible-galaxy collection install`` should warn about ``--collections-path`` missing from configured :ref:`collections_paths`.
;collections_path_warning=True

# (path) Collection skeleton directory to use as a template for the ``init`` action in ``ansible-galaxy collection``, same as ``--collection-skeleton``.
;collection_skeleton=

# (list) patterns of files to ignore inside a Galaxy collection skeleton directory.
;collection_skeleton_ignore=^.git$, ^.*/.git_keep$

# (bool) Disable GPG signature verification during collection installation.
;disable_gpg_verify=False

# (bool) Some steps in ``ansible-galaxy`` display a progress wheel which can cause issues on certain displays or when outputting the stdout to a file.
# This config option controls whether the display wheel is shown or not.
# The default is to show the display wheel if stdout has a tty.
;display_progress=

# (path) Configure the keyring used for GPG signature verification during collection installation and verification.
;gpg_keyring=

# (boolean) If set to yes, ansible-galaxy will not validate TLS certificates. This can be useful for testing against a server with a self-signed certificate.
;ignore_certs=

# (list) A list of GPG status codes to ignore during GPG signature verification. See L(https://github.com/gpg/gnupg/blob/master/doc/DETAILS#general-status-codes) for status code descriptions.
# If fewer signatures successfully verify the collection than `GALAXY_REQUIRED_VALID_SIGNATURE_COUNT`, signature verification will fail even if all error codes are ignored.
;ignore_signature_status_codes=

# (str) The number of signatures that must be successful during GPG signature verification while installing or verifying collections.
# This should be a positive integer or all to indicate all signatures must successfully validate the collection.
# Prepend + to the value to fail if no valid signatures are found for the collection.
;required_valid_signature_count=1

# (path) Role skeleton directory to use as a template for the ``init`` action in ``ansible-galaxy``/``ansible-galaxy role``, same as ``--role-skeleton``.
;role_skeleton=

# (list) patterns of files to ignore inside a Galaxy role or collection skeleton directory.
;role_skeleton_ignore=^.git$, ^.*/.git_keep$

# (string) URL to prepend when roles don't specify the full URI, assume they are referencing this server as the source.
;server=https://galaxy.ansible.com

# (list) A list of Galaxy servers to use when installing a collection.
# The value corresponds to the config ini header ``[galaxy_server.{{item}}]`` which defines the server details.
# See :ref:`galaxy_server_config` for more details on how to define a Galaxy server.
# The order of servers in this list is used as the order in which a collection is resolved.
# Setting this config option will ignore the :ref:`galaxy_server` config option.
;server_list=

# (int) The default timeout for Galaxy API calls. Galaxy servers that don't configure a specific timeout will fall back to this value.
;server_timeout=60

# (path) Local path to galaxy access token file
;token_path=/home/<USER>/.ansible/galaxy_token


[inventory]
# (string) This setting changes the behaviour of mismatched host patterns, it allows you to force a fatal error, a warning or just ignore it.
;host_pattern_mismatch=warning

# (boolean) If 'true', it is a fatal error when any given inventory source cannot be successfully parsed by any available inventory plugin; otherwise, this situation only attracts a warning.

;any_unparsed_is_failed=False

# (bool) Toggle to turn on inventory caching.
# This setting has been moved to the individual inventory plugins as a plugin option :ref:`inventory_plugins`.
# The existing configuration settings are still accepted with the inventory plugin adding additional options from inventory configuration.
# This message will be removed in 2.16.
;cache=False

# (string) The plugin for caching inventory.
# This setting has been moved to the individual inventory plugins as a plugin option :ref:`inventory_plugins`.
# The existing configuration settings are still accepted with the inventory plugin adding additional options from inventory and fact cache configuration.
# This message will be removed in 2.16.
;cache_plugin=

# (string) The inventory cache connection.
# This setting has been moved to the individual inventory plugins as a plugin option :ref:`inventory_plugins`.
# The existing configuration settings are still accepted with the inventory plugin adding additional options from inventory and fact cache configuration.
# This message will be removed in 2.16.
;cache_connection=

# (string) The table prefix for the cache plugin.
# This setting has been moved to the individual inventory plugins as a plugin option :ref:`inventory_plugins`.
# The existing configuration settings are still accepted with the inventory plugin adding additional options from inventory and fact cache configuration.
# This message will be removed in 2.16.
;cache_prefix=ansible_inventory_

# (string) Expiration timeout for the inventory cache plugin data.
# This setting has been moved to the individual inventory plugins as a plugin option :ref:`inventory_plugins`.
# The existing configuration settings are still accepted with the inventory plugin adding additional options from inventory and fact cache configuration.
# This message will be removed in 2.16.
;cache_timeout=3600

# (list) List of enabled inventory plugins, it also determines the order in which they are used.
;enable_plugins=host_list, script, auto, yaml, ini, toml

# (bool) Controls if ansible-inventory will accurately reflect Ansible's view into inventory or its optimized for exporting.
;export=False

# (list) List of extensions to ignore when using a directory as an inventory source.
;ignore_extensions=.pyc, .pyo, .swp, .bak, ~, .rpm, .md, .txt, .rst, .orig, .ini, .cfg, .retry

# (list) List of patterns to ignore when using a directory as an inventory source.
;ignore_patterns=

# (bool) If 'true' it is a fatal error if every single potential inventory source fails to parse, otherwise, this situation will only attract a warning.

;unparsed_is_failed=False

# (boolean) By default, Ansible will issue a warning when no inventory was loaded and notes that it will use an implicit localhost-only inventory.
# These warnings can be silenced by adjusting this setting to False.
;inventory_unparsed_warning=True


[netconf_connection]
# (string) This variable is used to enable bastion/jump host with netconf connection. If set to True the bastion/jump host ssh settings should be present in ~/.ssh/config file, alternatively it can be set to custom ssh configuration file path to read the bastion/jump host settings.
;ssh_config=


[paramiko_connection]
# (boolean) TODO: write it
;host_key_auto_add=False

# (boolean) TODO: write it
;look_for_keys=True


[jinja2]
# (list) This list of filters avoids 'type conversion' when templating variables.
# Useful when you want to avoid conversion into lists or dictionaries for JSON strings, for example.
;dont_type_filters=string, to_json, to_nice_json, to_yaml, to_nice_yaml, ppretty, json


[tags]
# (list) default list of tags to run in your plays, Skip Tags has precedence.
;run=

# (list) default list of tags to skip in your plays, has precedence over Run Tags
;skip=
