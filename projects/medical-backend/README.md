# 医疗后端基础设施剧本

这个 Ansible 框架提供了一种强大且幂等的方式来管理医疗后端服务，包括脚本执行、监控服务、数据库操作和服务重启。

## 📊 工作流程图

有关完整工作流程的可视化表示，请参见：**[工作流程图](workflow-diagram.md)**

## 功能特性

- **强大的脚本执行**: 包含错误处理、超时和适当的退出码管理
- **幂等操作**: 可安全地多次运行，结果一致
- **全面的日志记录**: 详细的执行日志，包含时间戳和状态跟踪
- **健康监控**: 内置健康检查和资源监控
- **灵活的目标定位**: 在特定清单组上执行脚本
- **备份管理**: 自动备份和清理旧的执行日志
- **服务管理**: 全面的服务重启和监控功能
- **数据库操作**: MongoDB 认证和配置管理
- **Grafana 集成**: 监控服务升级和配置更新

## 目录结构

```
projects/medical-backend/
├── ansible.cfg                              # Ansible 配置
├── site.yml                                 # 主剧本
├── README.md                                # 本文档
├── workflow-diagram.md                      # 可视化工作流程图
├── USAGE.md                                 # 使用示例和说明
├── inventory/                               # 清单配置
│   └── hosts.yml                           # 主机定义
├── extra-vars/                             # 外部变量文件
├── group_vars/                             # 组特定变量
├── keys/                                   # SSH 认证密钥
└── roles/
    ├── Dep.SetFacts/                       # 全局变量管理
    │   ├── tasks/main.yml
    │   └── vars/main.yml
    ├── Dep.SshConnection/                  # SSH 连接管理
    │   └── tasks/
    │       ├── main.yml
    │       └── task-known_hosts.yml
    ├── Dep.RepoOps/                        # 仓库操作
    │   └── tasks/
    │       ├── main.yml
    │       ├── task-git-config.yml
    │       └── task-sync-repo.yml
    ├── 01-common/                          # 主要脚本执行角色
    │   ├── meta/main.yml                   # 角色依赖
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   └── task-execute-shell-script.yml
    │   └── files/                          # 在此放置您的 shell 脚本
    │       ├── *.sh                        # 您的 shell 脚本
    │       └── base.sh                     # 基础 shell 脚本
    ├── 02-grafana/                         # 监控服务管理
    │   ├── meta/main.yml
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-upgrade_services.yml
    │   │   └── task-update_configs.yml
    │   └── files/
    │       └── docker-compose.yml
    ├── 03-database/                        # 数据库操作
    │   └── tasks/main.yml
    └── 04-restart-services/                # 服务重启管理
        ├── tasks/
        │   ├── main.yml
        │   └── task-restart-services.yml
        └── vars/
            └── services.yml                # 服务配置
```

## 先决条件

1. 在控制机器上安装 Ansible
2. 配置对目标服务器的 SSH 访问
3. 配置包含目标服务器组的清单文件
4. 设置 SSH 密钥进行认证

## 工作流程概述

医疗后端基础设施剧本支持多种运维工作流程：

### 1. 脚本执行工作流程
- **目的**: 在远程医疗后端服务器上执行 shell 脚本
- **关键组件**: 01-common 角色、脚本验证、执行日志
- **用法**: `ansible-playbook site.yml -e "script_name=your_script.sh target_group=your_group"`

### 2. 监控服务工作流程 (Grafana)
- **目的**: 管理监控基础设施和配置
- **关键组件**: 02-grafana 角色、服务升级、配置更新
- **用法**: `ansible-playbook site.yml --tags grafana,upgrade_services`

### 3. 数据库操作工作流程
- **目的**: 处理 MongoDB 认证和配置更新
- **关键组件**: 03-database 角色、认证管理
- **用法**: `ansible-playbook site.yml --tags database`

### 4. 服务重启工作流程
- **目的**: 通过基于组的管理在多个主机上重启服务
- **关键组件**: 04-restart-services 角色、动态主机定位
- **用法**: `ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group']"`

## 使用方法

### 📖 Detailed Usage Guide

For comprehensive usage instructions and examples, see: **[Usage Guide](usage.md)**

### Basic Script Execution

To execute a script on a specific group of servers:

```bash
ansible-playbook site.yml -e "script_name=your_script.sh target_group=your_group"
```

### Examples

#### Script Execution Examples
```bash
# Execute health check on all servers
ansible-playbook site.yml -e "target_group=all script_name=health_check.sh release_tag=0 remote_scripts_dir=/mnt/efs/production/devops/deploy"

# Execute deployment script with CLI command on backend servers
ansible-playbook site.yml -e "target_group=medical_servers script_name=backend.sh release_tag=v1.0.0 remote_scripts_dir=/mnt/efs/production/devops/deploy/bureau"

# Execute deployment script with extra-vars file on backend servers
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml"

# Execute with custom timeout (default is -1, no timeout)
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml" --extra-vars "medical.scripts.execution_timeout=600"
```

#### Monitoring Services (Grafana) Examples
```bash
# Upgrade monitoring services
ansible-playbook site.yml --tags grafana,upgrade_services

# Update monitoring configuration files
ansible-playbook site.yml --tags grafana,update_configs

# Both upgrade and update configurations
ansible-playbook site.yml --tags grafana
```

#### Database Operations Examples
```bash
# Execute database operations
ansible-playbook site.yml --tags database

# Database operations with custom variables
ansible-playbook site.yml --tags database -e "@extra-vars/database.yml"
```

#### Service Restart Examples
```bash
# Restart services by group
ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group', 'frontend_group']"

# Restart specific services
ansible-playbook site.yml --tags restart_services -e "restart_services=['service1', 'service2']"

# Restart with mixed groups and services
ansible-playbook site.yml --tags restart_services -e "restart_groups=['backend_group']" -e "restart_services=['specific_service']"
```

## Adding New Scripts

1. Place your shell script in `roles/01-common/files/`
2. Ensure the script is executable and follows best practices:
   - Include `#!/bin/bash` shebang
   - Use `set -euo pipefail` for error handling
   - Provide clear output and error messages
   - Exit with appropriate codes (0 for success, non-zero for failure)

Example script structure:

```bash
#!/bin/bash
set -euo pipefail

echo "Starting script execution..."

# Your script logic here

if [ $? -eq 0 ]; then
    echo "Script completed successfully"
    exit 0
else
    echo "Script failed"
    exit 1
fi
```

## Configuration

### Variables

Key configuration variables are defined in `roles/Dependencies/SetFacts/vars/main.yml`:

- `medical.scripts.execution_timeout`: Script execution timeout (default: 300 seconds)
- `medical.scripts.relative_local_scripts_dir`: Directory to store shell scripts (relative to medical.repo.ansible.repo_root_dir)

### Inventory Groups

Configure your inventory file with appropriate groups:

```ini
[production_servers]
bureau.prod
medical.prod

[testig_servers]
bureau.testing
medical.testing
```

Or using yaml file:

```yaml
all:
  children:
    production:
      children:
        production_servers:
          hosts:
            bureau.prod:
              ansible_host: ***********
              ansible_user: ubuntu
            medical.prod:
              ansible_host: ***********
              ansible_user: ubuntu
    testing:
      children:
        testing_servers:
          hosts:
            bureau.testing:
              ansible_host: ***********
              ansible_user: ubuntu
            medical.testing:
              ansible_host: ***********
              ansible_user: ubuntu
```

## Logging and Monitoring

### Execution Logs

- Each script execution creates a detailed log file in `{remote_scripts_dir}/`
- Log files are named: `{script_name}_{timestamp}.log`
- A master execution log tracks all script runs: `execution.log`

### Log Cleanup

- Old log files are automatically cleaned up (keeps last 10 executions per script)
- Manual cleanup can be performed by running the cleanup tasks

### Monitoring Script Execution

Check execution status:

```bash
# View recent executions
ansible all -m shell -a "tail -20 {remote_scripts_dir}/execution.log"

# Check specific script logs
ansible all -m shell -a "ls -la {remote_scripts_dir} | grep your_script"
```

## Error Handling

The framework includes comprehensive error handling:

1. **Pre-execution validation**: Checks if script exists and variables are defined
2. **Timeout protection**: Scripts are terminated if they exceed the timeout
3. **Exit code monitoring**: Script failures are properly detected and reported
4. **Detailed error reporting**: Clear error messages with troubleshooting information
5. **Log preservation**: All execution details are preserved for debugging

## Best Practices

1. **Test scripts locally** before deploying through Ansible
2. **Use meaningful script names** that describe their purpose
3. **Include proper error handling** in your scripts
4. **Monitor execution logs** regularly for issues
5. **Keep scripts idempotent** - safe to run multiple times
6. **Use appropriate timeouts** for long-running scripts
7. **Document script purposes** and expected outcomes

## Troubleshooting

### Common Issues

1. **Script not found**: Ensure the script exists in `roles/01-common/files/`
2. **Permission denied**: Check script permissions and SSH access
3. **Timeout errors**: Increase timeout value or optimize script performance
4. **SSH connection issues**: Verify inventory and SSH key configuration

### Debug Mode

Run with verbose output for troubleshooting:

```bash
ansible-playbook site.yml -e "@extra-vars/bureau.backend.yml" -vvv
```

## Security Considerations

- Scripts are executed with appropriate permissions
- SSH keys should be properly secured
- Execution logs may contain sensitive information - secure log directories
- Review scripts for security implications before deployment
- Use sudo/become only when necessary

## Contributing

When adding new functionality:

1. Follow the existing code structure and naming conventions
2. Add appropriate error handling and logging
3. Update documentation and examples
4. Test thoroughly in a development environment
5. Consider backward compatibility
