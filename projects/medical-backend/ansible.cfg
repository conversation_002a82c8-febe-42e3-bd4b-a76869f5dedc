# Ansible Configuration for Medical Backend Project
# This file contains project-specific settings optimized for security, reliability and audit compliance

[defaults]
# Set default inventory file path to local inventory directory
# Points to the inventory files containing target host information
inventory=./inventory

# Set default roles path to local roles directory
# Defines where Ansible looks for role definitions and dependencies
roles_path=./roles

# Set default module to 'shell' for ad-hoc commands
# Useful for medical backend scenarios that require shell script execution
module_name=shell

# Disable SSH host key checking to avoid interactive prompts during automation
# Security Note: Only use in trusted medical infrastructure environments
host_key_checking = False

# Disable password prompts for SSH connections
# Assumes SSH key-based authentication is properly configured for security
ask_pass=False

# Set connection timeout to 90 seconds for reliable operations in medical infrastructure
# Higher timeout for medical backend due to security scanning and compliance checks
timeout = 90

# Enable smart fact gathering for medical backend
# Collects necessary system information while minimizing overhead
gathering = smart

# Specify which fact modules to use for gathering
# Allows fine-grained control over what information is collected
# Default: all available fact modules
# gather_subset = all

# Examples of gather_subset options:
# gather_subset = network,hardware          # Only network and hardware facts
# gather_subset = !facter,!ohai            # Exclude facter and ohai facts  
# gather_subset = min                       # Minimal fact set (fastest)
# gather_subset = network,!hardware         # Network facts but not hardware

# Gather subset for medical backend (commented examples)
# Uncomment and modify based on specific needs:
# gather_subset = min                       # Fastest - only essential facts
# gather_subset = network,virtual,hardware  # Common system information
# gather_subset = !facter,!ohai,!puppet    # Exclude external fact sources

# Timeout for fact gathering operations (seconds)
# Important for medical infrastructure with security scanning
# Note: gather_timeout is deprecated, using module_defaults instead

# Filter for fact gathering to include only specific facts
# Useful for reducing overhead and improving security
# Example: gather_subset = network,!all_ipv6_addresses
# filter_plugins = /path/to/custom/filter/plugins

# Set moderate parallel processes for medical backend stability
# Conservative setting for medical infrastructure to ensure reliability
forks = 15

# Specify log file location for Ansible execution logs with enhanced security
# Critical for audit compliance in medical environments
log_path = ./ansible.log

# Disable task debugger to prevent interactive debugging sessions
# Security requirement for automated medical backend operations
enable_task_debugger=False

# Keep deprecation warnings for medical compliance and security updates
# Important to track deprecated features for security compliance
deprecation_warnings=True

# Enable fact caching for medical backend efficiency
# Improves performance while maintaining audit trail
fact_caching = jsonfile
fact_caching_connection = /tmp/ansible_medical_fact_cache
fact_caching_timeout = 1800

# Add audit and security callback plugins for medical compliance
# Essential for tracking all operations in medical environments
callbacks_enabled = skippy

# Enhanced error handling for medical backend reliability
any_errors_fatal = False
max_fail_percentage = 10

# Retry configuration for medical backend resilience
retry_files_enabled = True
retry_files_save_path = ./medical_retries

# Security settings for medical data handling
command_warnings = True
system_warnings = True

# Vault settings for medical data encryption
vault_password_file = ./vault_pass.txt

[ssh_connection]
# Enhanced SSH connection optimization for medical backend security and reliability
# -C: Enable compression to reduce network bandwidth
# -o ControlMaster=auto: Enable SSH connection multiplexing for efficiency
# -o ControlPersist=180s: Keep master connection alive for 3 minutes (medical backend appropriate)
# -o ServerAliveInterval=30: Send keepalive every 30 seconds for connection monitoring
# -o ServerAliveCountMax=5: Allow 5 failed keepalives for medical infrastructure stability
# -o StrictHostKeyChecking=no: Consistent with host_key_checking=False but explicit
# -o ConnectTimeout=60: Explicit connection timeout for medical infrastructure
ssh_args = -C -o ControlMaster=auto -o ControlPersist=180s -o ServerAliveInterval=30 -o ServerAliveCountMax=5 -o StrictHostKeyChecking=no -o ConnectTimeout=60

# Enable SSH pipelining for efficient script execution
# Critical for medical backend script execution performance
pipelining = True

# Set secure SSH control path for medical environments
# Uses dedicated directory structure for medical backend operations
control_path = /tmp/ansible-medical-ssh-%%h-%%p-%%r-$$

# Enhanced connection retry for medical backend reliability
retries = 5

# SSH connection timeout settings
connect_timeout = 60
connect_retry_timeout = 30

[privilege_escalation]
# Enable privilege escalation for medical backend administrative tasks
# Medical backend often requires elevated privileges for system operations
become=True
become_method=sudo
become_user=root
# Security: Don't prompt for sudo password (assumes secure NOPASSWD configuration)
become_ask_pass=False
# Additional timeout for privilege escalation in medical environments
become_timeout=60

[inventory]
# Enable comprehensive inventory plugins for medical backend
enable_plugins = host_list, script, auto, yaml, ini, constructed

[colors]
# Enable professional colored output for medical backend operations
highlight = white
verbose = blue
warn = bright purple
error = red
debug = dark gray
deprecate = purple
skip = cyan
unreachable = red
ok = green
changed = yellow

[persistent_connection]
# Enhanced persistent connection settings for medical backend
# Optimized for long-running medical backend operations
connect_timeout = 60
command_timeout = 300

# Galaxy configuration removed to avoid configuration conflicts

[netconf_connection]
# Network configuration connection settings for medical infrastructure
# Enhanced timeouts for medical network equipment
persistent_connect_timeout = 60
persistent_command_timeout = 300
