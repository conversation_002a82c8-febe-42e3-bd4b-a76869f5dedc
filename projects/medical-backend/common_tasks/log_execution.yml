---
# Common Tasks: Execution Logging
# This file provides standardized logging functionality for all medical backend operations
# Supports flexible log formats, file management, and cleanup operations

- name: Ensure logging prerequisites
  ansible.builtin.include_tasks: ensure_timestamp.yml
  tags:
    - logging
    - prerequisites

- name: Create log directory
  ansible.builtin.file:
    path: "{{ log_directory | default('/tmp/ansible-logs') }}"
    state: directory
    mode: "{{ common_file_permissions.directory_mode | default('0755') }}"
  become: false
  when: create_log_directory | default(true)
  tags:
    - logging
    - setup

- name: Log execution event
  ansible.builtin.lineinfile:
    path: "{{ log_file_path }}"
    line: "{{ log_entry }}"
    create: true
    mode: "{{ common_file_permissions.log_mode | default('0644') }}"
  become: false
  when: log_entry is defined
  tags:
    - logging
    - event

- name: Log execution event with standard format
  ansible.builtin.lineinfile:
    path: "{{ log_file_path }}"
    line: "{{ standard_timestamp_iso }} - {{ log_host | default(ansible_hostname | default(inventory_hostname)) }} - {{ log_component | default('UNKNOWN') }} - {{ log_status | default('INFO') }} - {{ log_message | default('No message') }}"
    create: true
    mode: "{{ common_file_permissions.log_mode | default('0644') }}"
  become: false
  when:
    - log_file_path is defined
    - use_standard_format | default(false)
  tags:
    - logging
    - standard

- name: Log detailed execution information
  ansible.builtin.blockinfile:
    path: "{{ log_file_path }}"
    marker: "# {mark} {{ log_component | default('COMPONENT') }} EXECUTION {{ standard_timestamp_epoch }}"
    block: |
      Execution Details:
      ==================
      Timestamp: {{ standard_timestamp_iso }}
      Host: {{ log_host | default(ansible_hostname | default(inventory_hostname)) }}
      Component: {{ log_component | default('UNKNOWN') }}
      Status: {{ log_status | default('INFO') }}
      Message: {{ log_message | default('No message') }}
      {% if log_details is defined %}
      Additional Details:
      {{ log_details | indent(2) }}
      {% endif %}
      {% if log_output is defined %}
      Output:
      {{ log_output | indent(2) }}
      {% endif %}
      {% if log_error is defined %}
      Error Information:
      {{ log_error | indent(2) }}
      {% endif %}
    create: true
    mode: "{{ common_file_permissions.log_mode | default('0644') }}"
  become: false
  when:
    - log_file_path is defined
    - use_detailed_format | default(false)
  tags:
    - logging
    - detailed

- name: Clean up old log files
  ansible.builtin.shell: |
    set -euo pipefail
    cd "{{ log_directory | default('/tmp/ansible-logs') }}"
    # Keep only the most recent log files based on pattern
    if [ -n "{{ log_cleanup_pattern | default('') }}" ]; then
      ls -1t {{ log_cleanup_pattern }} 2>/dev/null | tail -n +{{ (common_logging.keep_log_count | default(20)) + 1 }} | xargs -r rm -f || true
    fi
  become: false
  changed_when: false
  failed_when: false
  when:
    - cleanup_old_logs | default(false)
    - log_directory is defined
  args:
    executable: /bin/bash
  tags:
    - logging
    - cleanup

- name: Display logging summary
  ansible.builtin.debug:
    msg: |
      Logging Summary:
      ===============
      Log File: {{ log_file_path | default('Not specified') }}
      Log Directory: {{ log_directory | default('/tmp/ansible-logs') }}
      Format Used: {{ 'Standard' if use_standard_format | default(false) else 'Detailed' if use_detailed_format | default(false) else 'Custom' }}
      Component: {{ log_component | default('UNKNOWN') }}
      Status: {{ log_status | default('INFO') }}
      Cleanup Enabled: {{ cleanup_old_logs | default(false) }}
  when: show_logging_summary | default(false)
  tags:
    - logging
    - summary
# Example usage patterns for this logging file:
#
# Simple event logging:
# - ansible.builtin.include_tasks: common_tasks/log_execution.yml
#   vars:
#     log_file_path: "/path/to/logfile.log"
#     log_entry: "{{ ansible_date_time.iso8601 }} - Event occurred"
#
# Standard format logging:
# - ansible.builtin.include_tasks: common_tasks/log_execution.yml
#   vars:
#     log_file_path: "/path/to/logfile.log"
#     use_standard_format: true
#     log_component: "SERVICE_RESTART"
#     log_status: "SUCCESS"
#     log_message: "Service restarted successfully"
#
# Detailed logging with cleanup:
# - ansible.builtin.include_tasks: common_tasks/log_execution.yml
#   vars:
#     log_file_path: "/path/to/logfile.log"
#     use_detailed_format: true
#     log_component: "SCRIPT_EXECUTION"
#     log_status: "FAILED"
#     log_message: "Script execution failed"
#     log_details: "Additional context information"
#     log_output: "Script output content"
#     log_error: "Error message details"
#     cleanup_old_logs: true
#     log_cleanup_pattern: "script-*.log"
