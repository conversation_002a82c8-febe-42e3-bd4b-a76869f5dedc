---
# Common Tasks: Parameter Validation
# This file provides standardized parameter validation functionality
# Supports flexible validation conditions and customizable error messages

- name: Validate required parameters
  ansible.builtin.fail:
    msg: "{{ validation_error_message | default('Required parameters are missing or invalid') }}"
  when: validation_condition | default(false)
  tags:
    - validation
    - parameters

- name: Validate parameter types and values
  ansible.builtin.fail:
    msg: |
      Parameter validation failed:
      {{ validation_details | default('Invalid parameter type or value') }}
  when:
    - validation_type_condition is defined
    - validation_type_condition | default(false)
  tags:
    - validation
    - types

- name: Display parameter validation summary
  ansible.builtin.debug:
    msg: |
      Parameter Validation Summary:
      ============================
      Validation Status: PASSED
      Checked Parameters: {{ validated_parameters | default(['No parameters specified']) | join(', ') }}
      Validation Rules: {{ validation_rules | default(['No rules specified']) | join(', ') }}
  when:
    - validation_summary | default(false)
    - not (validation_condition | default(false))
    - not (validation_type_condition | default(false))
  tags:
    - validation
    - summary
# Example usage patterns for this validation file:
#
# To validate that at least one parameter is provided:
# - ansible.builtin.include_tasks: common_tasks/validate_parameters.yml
#   vars:
#     validation_condition: "{{ (param1 | default([]) | length == 0) and (param2 | default([]) | length == 0) }}"
#     validation_error_message: "At least one of param1 or param2 must be specified"
#     validated_parameters: ["param1", "param2"]
#     validation_rules: ["at_least_one_required"]
#     validation_summary: true
#
# To validate parameter types:
# - ansible.builtin.include_tasks: common_tasks/validate_parameters.yml
#   vars:
#     validation_type_condition: "{{ param1 is not string }}"
#     validation_details: "param1 must be a string, got: {{ param1 | type_debug }}"
#     validated_parameters: ["param1"]
#     validation_rules: ["type_check"]
