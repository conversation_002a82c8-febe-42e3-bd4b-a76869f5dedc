---
# Common Tasks: Timestamp Management
# This file ensures that ansible_date_time is available for all operations
# Provides fallback mechanisms for reliable timestamp generation

- name: Ensure ansible_date_time is available
  ansible.builtin.setup:
    gather_subset:
      - "min"
      - "date_time"
  when: ansible_date_time is not defined
  tags:
    - timestamp
    - setup

- name: Get current timestamp fallback
  ansible.builtin.command: date -u +"%Y-%m-%dT%H:%M:%SZ"
  register: current_timestamp_iso
  changed_when: false
  when: ansible_date_time is not defined
  tags:
    - timestamp
    - fallback

- name: Get epoch timestamp fallback
  ansible.builtin.command: date +%s
  register: current_timestamp_epoch
  changed_when: false
  when: ansible_date_time is not defined
  tags:
    - timestamp
    - fallback

- name: Set standardized timestamp facts
  ansible.builtin.set_fact:
    standard_timestamp_iso: '{{ ansible_date_time.iso8601 | default(current_timestamp_iso.stdout) | default(lookup(''pipe'', ''date -u +"%Y-%m-%dT%H:%M:%SZ"'')) }}'
    standard_timestamp_epoch: "{{ ansible_date_time.epoch | default(current_timestamp_epoch.stdout) | default(lookup('pipe', 'date +%s')) }}"
    standard_timestamp_human: "{{ ansible_date_time.date | default('Unknown') }} {{ ansible_date_time.time | default('Unknown') }}"
  tags:
    - timestamp
    - facts

- name: Debug timestamp information
  ansible.builtin.debug:
    msg: |
      Timestamp Information:
      =====================
      ISO 8601: {{ standard_timestamp_iso }}
      Epoch: {{ standard_timestamp_epoch }}
      Human Readable: {{ standard_timestamp_human }}
      Source: {{ 'ansible_date_time' if ansible_date_time is defined else 'command fallback' }}
  when: ansible_verbosity >= 2
  tags:
    - timestamp
    - debug
