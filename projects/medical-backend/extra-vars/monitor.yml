---
# Image name and version of the services
grafana_image_name: grafana/grafana
grafana_version: "12.0.1"

loki_image_name: grafana/loki
loki_version: "3.5.1"

promtail_image_name: grafana/promtail
promtail_version: "3.5.1"

prometheus_image_name: prom/prometheus
prometheus_version: "v3.4.1"

node_exporter_image_name: prom/node-exporter
node_exporter_version: "v1.9.1"

# Whether to sync docker-compose.yml to remote hosts
sync_docker_compose: true

# Relative path to the config files
relative_prometheus_config_dir: prometheus/config
relative_redis_monitor_config_dir: service/redis/conf
relative_loki_config_dir: loki/config
