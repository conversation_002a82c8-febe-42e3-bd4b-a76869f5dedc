---
# MongoDB Authentication Update Configuration
# This file provides example configuration for updating authentication strings across all medical backend services

# String replacement parameters
# Example: Update MongoDB connection string with new credentials
old_string: "yakumaru:1111111111111111"
new_string: "yakumaru-new:2222222222222222"
# Alternative examples (uncomment and modify as needed):
#
# Example 1: Update database password
# old_string: "DB_PASSWORD=old_password_123"
# new_string: "DB_PASSWORD=new_secure_password_456"
#
# Example 2: Update MongoDB connection URI
# old_string: "*************************************************"
# new_string: "*************************************************"
#
# Example 3: Update API key
# old_string: "API_KEY=old_api_key_value"
# new_string: "API_KEY=new_api_key_value"

# Note: The services configuration is defined in roles/Dependencies/SetFacts/vars/main.yml
# and includes all servers and files from mongo-auth-update.md:
#
# Services that will be processed:
# - 贤太API集群: *********** (/mnt/efs/production/devops/deploy/api/.env)
# - 事务局: *********** (multiple .env files + docker-compose restart)
# - 贤太识别端: ************ (/mnt/efs/production/www/neox-med-backend/.env + docker-compose restart)
# - Smart药局: *********** (/mnt/efs/production/bureau/neox-smart-backend/.env)
# - GreenMedic: **********, *********** (/var/greenmedic/neox-greenmedic-backend/.env)
# - selector环境: *********** (/mnt/efs/production/selector/neox-ocr/.env)
# - neox-inc官网: ************* (/usr/share/nginx/neox-homepage-docker/.env)
# - smart药局统计执行文件相关: *********** (/mnt/efs/production/bureau/prod.yml)
