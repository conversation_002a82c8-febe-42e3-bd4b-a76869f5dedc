---
# Medical Backend Infrastructure Inventory
# Organized by environment with service type classification

all:
  children:
    production:
      children:
        homepage_servers:
          hosts:
            *************:
              ansible_python_interpreter: /home/<USER>/miniconda3/bin/python3
        medical_servers:
          hosts:
            ***********:
        recognize_servers:
          hosts:
            ************:
        grafana_servers:
          hosts:
            ************:
      vars:
        # Production environment variables
        ansible_user: "ubuntu"
        deploy_env: production
        deploy_branch: main
        log_level: warn
        backup_enabled: true
  vars:
    # Global variables for all servers
    ansible_ssh_private_key_file: keys/neox-jp.pem
    ansible_ssh_common_args: "-o StrictHostKeyChecking=no"
    # Global python interpreter, can be overridden per host
    ansible_python_interpreter: /usr/bin/python3
    project_name: medical-backend
