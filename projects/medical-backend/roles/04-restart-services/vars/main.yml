---
services_info:
    restart:
        # 默认配置
        defaults:
            remote_logs_dir: "/mnt/efs/production/devops/logs/ansible-service-restart-logs"
            execution_timeout: -1 # -1表示无超时限制，其他数值为超时秒数
            max_retry_attempts: 3
            retry_delay: 10 # 重试间隔10秒

        # 所有服务的统一配置定义（避免重复定义）
        services:
            async-merge:
                host: ***********
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart async-merge
            async-dispatcher:
                host: ***********
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart async-dispatcher
            med-queue:
                host: ***********
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart med-queue
            async-recognize:
                host: ************
                path: /mnt/efs/production/www/medical-docker
                command: docker-compose restart async-recognize
            selector:
                host: ***********
                path: /mnt/efs/production/selector/medical-docker
                command: docker-compose restart selector
            php-fpm-74:
                host: ***********
                path: /home/<USER>/medical-docker
                command: docker-compose restart php-fpm-74
            nginx:
                host: ***********
                path: /home/<USER>/medical-docker
                command: docker-compose restart nginx
            bureau-queue:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart bureau-queue
            med-cover-original:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart med-cover-original
            prescription:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart prescription
            prescription-index:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart prescription-index
            smart-queue:
                host: ***********
                path: /mnt/efs/production/bureau/medical-docker
                command: docker-compose restart smart-queue

        # 按发布端分组的服务映射（引用上面的服务定义）
        groups:
            recognize:
                - async-merge
                - async-dispatcher
                - med-queue
                - async-recognize
            qps:
                - selector
            api:
                - php-fpm-74
                - nginx
                - med-queue
            bureau:
                - bureau-queue
                - med-cover-original
                - prescription
                - prescription-index
            smart:
                - smart-queue
