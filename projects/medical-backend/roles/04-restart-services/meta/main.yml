---
galaxy_info:
  role_name: restart_services
  author: Medical Backend Team
  description: Service restart management for medical backend infrastructure
  company: NeoX Inc
  license: MIT
  min_ansible_version: "2.9"
  platforms:
    - name: Ubuntu
      versions:
        - bionic
        - focal
        - jammy
  galaxy_tags:
    - medical
    - backend
    - services
    - restart
    - docker

allow_duplicates: false
dependencies:
  - role: Dep.SetFacts
