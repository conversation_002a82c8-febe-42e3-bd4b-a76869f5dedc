---
# Process single service - handles multiple IPs and files
# This file is included for each service. It expects 'current_service' from the parent loop.

- name: "Service: Validate service configuration: {{ current_service.service_name }}"
  ansible.builtin.assert:
    that:
      - current_service.service_name is defined
      - current_service.service_name != ""
      - current_service.ips is defined
      - current_service.ips | length > 0
      - current_service.files is defined
      - current_service.files | length > 0
      - current_service.sudo is defined
    fail_msg: |
      Service configuration validation failed for: {{ current_service.service_name | default('Unknown') }}
      Required fields: service_name, ips (non-empty), files (non-empty), sudo
    success_msg: "Service configuration validated: {{ current_service.service_name }}"

- name: "Service: Display service processing info: {{ current_service.service_name }}"
  ansible.builtin.debug:
    msg: |
      Processing Service: {{ current_service.service_name }}
      =================================================
      IPs to process: {{ current_service.ips | join(', ') }} ({{ current_service.ips | length }} IPs)
      Files per IP: {{ current_service.files | join(', ') }} ({{ current_service.files | length }} files)
      Sudo required: {{ current_service.sudo | default(false) }}
      Notes: {{ current_service.notes | default('None') }}

# Process each IP for the current service
- name: "Service: Process each IP: {{ current_service.service_name }}"
  ansible.builtin.include_tasks: task-process-single-ip.yml
  loop: "{{ current_service.ips }}"
  loop_control:
    loop_var: current_ip
    label: "{{ current_service.service_name }} - {{ current_ip }}"
  vars:
    service_files: "{{ current_service.files }}"
    service_name: "{{ current_service.service_name }}"
    service_sudo: "{{ current_service.sudo | default(false) }}"
