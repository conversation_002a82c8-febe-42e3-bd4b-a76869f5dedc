---
galaxy_info:
  role_name: database
  author: Medical Backend Team
  description: Database configuration and management for medical backend
  company: NeoX Inc
  license: MIT
  min_ansible_version: "2.9"
  platforms:
    - name: Ubuntu
      versions:
        - bionic
        - focal
        - jammy
  galaxy_tags:
    - medical
    - backend
    - database
    - mongodb
    - configuration

allow_duplicates: false
dependencies:
  - role: Dep.SetFacts
