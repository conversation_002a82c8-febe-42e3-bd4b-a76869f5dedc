---
galaxy_info:
  role_name: dep_sshconnection
  author: Medical Backend Team
  description: Manages SSH connections and known hosts for medical backend
  company: NeoX Inc
  license: MIT
  min_ansible_version: "2.9"
  platforms:
    - name: Ubuntu
      versions:
        - bionic
        - focal
        - jammy
  galaxy_tags:
    - medical
    - backend
    - ssh
    - connection
    - dependency

dependencies: []
