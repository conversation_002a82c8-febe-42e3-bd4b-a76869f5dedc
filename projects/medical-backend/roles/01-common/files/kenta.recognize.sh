#!/bin/bash
set -euo pipefail
set -x

# 参数检查
if [ $# -ne 1 ]; then
  echo "Usage: $0 <git_tag>"
  exit 1
fi

git_tag="$1"
project_name="neox-med-backend"

# 设置路径（供 base.sh 使用）
BASE_PATH="/mnt/efs/production/www"
DOCKER_PATH="${BASE_PATH}/${project_name}"

# 引入 base.sh 函数
source "$(dirname "$0")/base.sh"

# 进入项目目录
into_directory "$project_name"

# 记录当前 commit
OLD_COMMIT=$(git rev-parse HEAD)

# 切换 Git tag
checkout_tag "$git_tag"

# 如果 composer 文件有改动，就更新依赖
if diff_composer "$OLD_COMMIT"; then
  update_composer "$project_name"
fi

echo "[DONE] $project_name deployment of tag $git_tag completed."