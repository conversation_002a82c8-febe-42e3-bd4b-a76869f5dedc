#!/bin/bash
set -euo pipefail
set -x

# 参数检查
if [ $# -ne 1 ]; then
  echo "Usage: $0 <git_tag>"
  exit 1
fi

git_tag="$1"
project_name="neox-smart-line"

# 覆盖 base.sh 的路径变量
BASE_PATH="/mnt/efs/production/bureau"
DOCKER_PATH="${BASE_PATH}/${project_name}"

# 引入通用函数
source "$(dirname "$0")/base.sh"

# 进入项目目录
into_directory "$project_name"

# 记录当前 commit
OLD_COMMIT=$(git rev-parse HEAD)

# 切换到指定 tag
checkout_tag "$git_tag"

echo "[DONE] $project_name deployment of tag $git_tag completed (no service restart)."