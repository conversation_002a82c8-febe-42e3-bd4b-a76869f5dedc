#!/bin/bash

set -euo pipefail
set -x

# 参数检查
if [ $# -ne 1 ]; then
  echo "Usage: $0 <git_tag>"
  exit 1
fi

git_tag="$1"
project_name="bureau-frontend"

# 覆盖 base.sh 中的路径变量（以当前项目为准）
BASE_PATH="/mnt/efs/production/bureau"
DOCKER_PATH="${BASE_PATH}/${project_name}"  # 可选，不使用也不影响

# 加载通用函数
source "$(dirname "$0")/base.sh"

# 进入项目目录
into_directory "$project_name"

# 获取当前 commit（可选）
OLD_COMMIT=$(git rev-parse HEAD)

# 切换到指定 tag（使用 base.sh 封装函数）
checkout_tag "$git_tag"

echo "[DONE] $project_name deployment of tag $git_tag completed."