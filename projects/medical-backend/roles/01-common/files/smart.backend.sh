#!/bin/bash

set -euo pipefail
set -x
git_tag="$1"
project_name=neox-smart-backend
source "$(dirname "$0")/base.sh"

# 进入项目目录
into_directory $project_name
# 记录切换之前的commit
OLD_COMMIT=$(git rev-parse HEAD)
# 切换到指定tag
checkout_tag $git_tag
# 检查composer.json是否有变化
if diff_composer $OLD_COMMIT; then
  update_composer $project_name;
fi

echo "[DONE] $project_name deployment of tag $git_tag completed (no service restart)."