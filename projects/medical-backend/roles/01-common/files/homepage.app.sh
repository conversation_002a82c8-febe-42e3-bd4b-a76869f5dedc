#!/bin/bash
set -euo pipefail
set -x

# 参数检查
if [ $# -ne 1 ]; then
  echo "Usage: $0 <git_tag>"
  exit 1
fi

git_tag="$1"
project_name="neox-homepage-docker"

# ✅ 动态覆盖 base.sh 中的路径变量
BASE_PATH="/usr/share/nginx"
DOCKER_PATH="/usr/share/nginx/neox-homepage-docker"

# 加载共通函数（会使用上面这两个变量）
source "$(dirname "$0")/base.sh"

# 进入项目目录（会用到 BASE_PATH）
into_directory "$project_name"

# 记录当前 commit（切换前）
OLD_COMMIT=$(git rev-parse HEAD)

# 切换到指定 tag
checkout_tag "$git_tag"

# 检查 composer 文件是否变更
if diff_composer "$OLD_COMMIT"; then
  update_composer "$project_name"
fi

echo "[DONE] $project_name deployment of tag $git_tag completed (no service restart)."