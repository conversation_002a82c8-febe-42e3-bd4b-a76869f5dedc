#!/bin/bash
set -euo pipefail
IFS=$'\n\t'

# 原：
# BASE_PATH="/mnt/efs/production/bureau"
# DOCKER_PATH="${BASE_PATH}/medical-docker"

# 建议改成「只在未定义时赋默认值」：
BASE_PATH="${BASE_PATH:-/mnt/efs/production/bureau}"
DOCKER_PATH="${DOCKER_PATH:-${BASE_PATH}/medical-docker}"

# === 进入指定的项目目录 ===
into_directory() {
  local project="$1"
  local target_path="${BASE_PATH}/${project}"

  if [ -d "$target_path" ]; then
    cd "$target_path" || exit 1
  else
    echo "[ERROR] Directory not found: $target_path"
    exit 1
  fi
}

# === 检出指定 Git 标签 ===
checkout_tag() {
  local git_tag="$1"
  echo "[INFO] Fetching Git tags..."
  git fetch --tags

  echo "[INFO] Checking out tag: $git_tag"
  if git rev-parse --verify "$git_tag" >/dev/null 2>&1; then
    git checkout "$git_tag"
  else
    git checkout -b "$git_tag" "refs/tags/$git_tag"
  fi
}

# === 比较 composer 文件是否有变化 ===
diff_composer() {
  local base_commit="$1"

  if git diff --quiet "$base_commit" HEAD -- composer.json composer.lock; then
    echo "[INFO] No changes in composer.json or composer.lock"
    return 1
  else
    echo "[INFO] Changes detected in composer.json or composer.lock"
    return 0
  fi
}

# === 在容器中执行 composer install ===
update_composer() {
  local project_name="$1"
  echo "[INFO] Updating composer dependencies for: $project_name"

  cd "$DOCKER_PATH" || exit 1

  docker-compose exec -T php-fpm-74 bash -c "
    set -e
    cd /data/www/${project_name}
    composer install
  "
}