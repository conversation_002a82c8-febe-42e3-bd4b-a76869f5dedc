---
# This file is included for each service. It expects 'service_name' from the loop.
- name: "Check Service | Get current image for container: {{ service_name }}"
  ansible.builtin.command: "docker inspect --format='{{ '{{' }}.Config.Image{{ '}}' }}' {{ service_name }}"
  register: current_image_raw
  changed_when: false
  failed_when: false
  become: false

- name: "Check Service | Determine if upgrade needed: {{ service_name }}"
  vars:
    expected_image: "{{ vars[service_name.replace('-', '_') ~ '_image_name'] }}:{{ vars[service_name.replace('-', '_') ~ '_version'] }}"
  ansible.builtin.set_fact:
    is_upgrade_needed: "{{ current_image_raw.rc != 0 or current_image_raw.stdout != expected_image }}"
    current_image: "{{ current_image_raw.stdout | default('Not running or does not exist') }}"

- name: "Execute Upgrade | Update service if needed: {{ service_name }}"
  ansible.builtin.command: "docker-compose up -d --force-recreate {{ service_name }}"
  args:
    chdir: "{{ MEDICAL.monitor.remote_root_path }}"
  register: compose_up_result
  when: is_upgrade_needed
  changed_when: true
  become: false

- name: "Upgrade Report | Service upgraded: {{ service_name }}"
  vars:
    expected_image: "{{ vars[service_name.replace('-', '_') ~ '_image_name'] }}:{{ vars[service_name.replace('-', '_') ~ '_version'] }}"
  ansible.builtin.debug:
    msg: |
      Service '{{ service_name }}' upgrade complete.
      {% if current_image_raw.rc != 0 %}
      Reason: Container does not exist or is not running.
      {% else %}
      Reason: Image outdated (Current: '{{ current_image }}', Target: '{{ expected_image }}').
      {% endif %}
      Docker Compose output:
      {{ compose_up_result.stdout | indent(2) }}
  when: is_upgrade_needed and compose_up_result is changed

- name: "Status Report | Service up to date: {{ service_name }}"
  vars:
    expected_image: "{{ vars[service_name.replace('-', '_') ~ '_image_name'] }}:{{ vars[service_name.replace('-', '_') ~ '_version'] }}"
  ansible.builtin.debug:
    msg: "Service '{{ service_name }}' is already up to date (Image: '{{ expected_image }}'). No action needed."
  when: not is_upgrade_needed
