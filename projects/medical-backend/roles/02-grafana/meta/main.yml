---
galaxy_info:
  role_name: grafana
  author: Medical Backend Team
  description: Grafana monitoring and visualization setup for medical backend
  company: NeoX Inc
  license: MIT
  min_ansible_version: "2.9"
  platforms:
    - name: Ubuntu
      versions:
        - bionic
        - focal
        - jammy
  galaxy_tags:
    - medical
    - backend
    - grafana
    - monitoring
    - visualization

allow_duplicates: false
dependencies:
  - role: Dep.SetFacts
