---
- name: Check latest repository status
  ansible.builtin.stat:
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
  register: repo_status

- name: Setup SSH keys for repository access
  block:
    - name: Ensure .ssh directory exists
      ansible.builtin.file:
        path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh"
        state: directory
        mode: "0700"

    - name: Copy SSH keys (if they exist)
      ansible.builtin.copy:
        src: "{{ item[0] }}"
        dest: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/.ssh/{{ item[0] | basename }}"
        mode: "{{ item[1] }}"
        force: true
      loop:
        - - "roles/Dependencies/SshConnection/files/id_rsa"
          - "0600"
        - - "roles/Dependencies/SshConnection/files/id_rsa.pub"
          - "0644"
        - - "roles/Dependencies/SshConnection/files/config"
          - "0600"
      failed_when: false

- name: Ensure repository parent directory exists
  ansible.builtin.file:
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    state: directory
    mode: "0755"

- name: Continue if repository directory exists
  when: repo_status.stat.exists
  block:
    - name: Pull latest changes
      ansible.builtin.git:
        repo: "{{ MEDICAL.repo.ansible.address | default('*****************:neox-inc/ansible.git') }}"
        dest: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
        update: true
        accept_hostkey: true
        force: true
      failed_when: false

    - name: Ensure repository directory permissions
      ansible.builtin.file:
        path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
        state: directory
        mode: "0755"
        recurse: true
