---
- name: Ensure git is installed
  package:
    name: git
    state: present
  become: true

- name: Configure git user name
  git_config:
    name: user.name
    scope: global
    value: "Medical Backend Automation"

- name: Configure git user email
  git_config:
    name: user.email
    scope: global
    value: "<EMAIL>"

- name: Configure git to trust the repository directory
  git_config:
    name: safe.directory
    scope: global
    value: "{{ MEDICAL.home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"

- name: Configure git credential helper timeout
  git_config:
    name: credential.helper
    scope: global
    value: "cache --timeout=3600"

- name: Check if ansible repository directory exists
  ansible.builtin.stat:
    path: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
  register: ansible_repo_dir
  delegate_to: localhost

- name: Check if ansible repository is a git repository
  ansible.builtin.shell: "cd {{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }} && git rev-parse --is-inside-work-tree"
  register: is_git_repo
  changed_when: false
  failed_when: false
  delegate_to: localhost
  when: ansible_repo_dir.stat.exists

- name: Configure core.filemode status
  ansible.builtin.git_config:
    name: core.filemode
    value: "true"
    repo: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    scope: local
  changed_when: true
  ignore_errors: true
  register: filemode_config
  delegate_to: localhost
  when: ansible_repo_dir.stat.exists and is_git_repo.rc == 0

- name: Verify core.filemode configuration
  ansible.builtin.debug:
    msg: "Failed to configure core.filemode"
  when: filemode_config is defined and filemode_config is failed
  delegate_to: localhost

- name: Configure .githooks to core.hooksPath
  ansible.builtin.git_config:
    name: core.hooksPath
    value: ".githooks"
    repo: "{{ MEDICAL.repo.ansible.semaphore_home_path }}/{{ MEDICAL.repo.ansible.repo_root_dir }}"
    scope: local
  changed_when: true
  ignore_errors: true
  register: hooks_config
  delegate_to: localhost
  when: ansible_repo_dir.stat.exists and is_git_repo.rc == 0

- name: Verify core.hooksPath configuration
  ansible.builtin.debug:
    msg: "Failed to configure core.hooksPath"
  when: hooks_config is defined and hooks_config is failed
  delegate_to: localhost

- name: Skip git configuration when repository does not exist or is not a git repository
  ansible.builtin.debug:
    msg: "Skipping git configuration because repository is not valid"
  when: not ansible_repo_dir.stat.exists or (is_git_repo is defined and is_git_repo.rc != 0)
  delegate_to: localhost
