# 性能测试基础设施

这个 Ansible 框架提供了使用 Locust 进行综合负载测试场景的性能测试基础设施的自动化部署和管理。

## 📊 工作流程图

有关完整工作流程的可视化表示，请参见：**[工作流程图](workflow-diagram.md)**

## 功能特性

- **自动化基础设施部署**: 性能测试环境的完整设置
- **Locust 集成**: 内置对 Locust 负载测试框架的支持
- **灵活配置**: 通过 extra-vars 支持多种测试场景
- **可扩展架构**: 支持使用生成器节点的分布式测试
- **环境隔离**: 独立的准备和部署阶段
- **Conda 环境管理**: 测试工具的自动化 Python 环境设置
- **三阶段部署**: 基础设施准备、生成器部署和测试执行
- **多种运行类型**: 支持启动、停止和备份操作
- **调试模式**: 用于故障排除的实时容器日志监控

## 目录结构

```
projects/performance/
├── ansible.cfg                              # Ansible configuration
├── site.yml                                 # Main playbook
├── README.md                                # This documentation
├── workflow-diagram.md                      # Visual workflow diagram
├── vault_pass.txt                          # Vault password file
├── inventory/                               # Inventory files for target hosts
│   └── servers.yml                         # Server definitions
├── keys/                                    # SSH keys for authentication
├── extra-vars/                             # External variable configurations
│   └── locust/                             # Locust-specific configurations
│       ├── locust-standalone-enquete.yml   # Enquete scenario configuration
│       └── locust-standalone-pres.yml      # Presentation scenario configuration
└── roles/
    ├── Dep.SetFacts/                       # Global variable management
    │   ├── tasks/main.yml
    │   └── vars/main.yml                   # Performance testing variables
    ├── Dep.SshConnection/                  # SSH connection management
    │   └── tasks/
    │       ├── main.yml
    │       └── task-known_hosts.yml
    ├── Dep.RepoOps/                        # Repository operations
    │   └── tasks/
    │       ├── main.yml
    │       ├── task-git-config.yml
    │       └── task-sync-repo.yml
    ├── 01-prepare/                         # Infrastructure preparation
    │   ├── meta/main.yml                   # Role dependencies
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-apt_update_upgrade.yml
    │   │   ├── task-install_docker_dep.yml
    │   │   └── task-install_pip3.yml
    │   └── handlers/main.yml
    ├── 02-generator/                       # Generator service deployment
    │   ├── meta/main.yml
    │   ├── tasks/
    │   │   ├── main.yml
    │   │   ├── task-config-generator.yml
    │   │   └── task-build-locust.yml
    │   ├── templates/
    │   │   └── auto-install-miniconda3.sh.j2
    │   └── handlers/main.yml
    └── 03-locust/                          # Locust testing framework
        ├── tasks/
        │   ├── main.yml
        │   └── locust-standalone.yml
        └── vars/main.yml                   # Locust configuration variables
```

## 先决条件

1. 在控制机器上安装 Ansible
2. 配置对目标生成器服务器的 SSH 访问
3. 配置包含生成器组的清单文件
4. 设置 SSH 密钥进行认证
5. 具有足够资源进行负载测试的目标服务器

## 工作流程概述

性能测试基础设施遵循三阶段方法：

### 阶段 1: 基础设施准备 (01-prepare)
- **目的**: 设置基本基础设施和依赖项
- **组件**: 系统更新、Docker 安装、Python pip、Miniconda3
- **用法**: `ansible-playbook site.yml -t play_prepare`

### 阶段 2: 生成器服务部署 (02-generator)
- **目的**: 部署和配置测试生成器服务
- **组件**: Conda 环境设置、Locust Docker 镜像构建
- **用法**: `ansible-playbook site.yml -t play_generator`

### 阶段 3: Locust 性能测试 (03-locust)
- **目的**: 使用各种配置执行实际性能测试
- **组件**: 测试执行、监控、结果管理
- **运行类型**:
  - **启动**: `ansible-playbook site.yml -t locust_run -e "run_type=start"`
  - **停止**: `ansible-playbook site.yml -t locust_run -e "run_type=stop"`
  - **备份**: `ansible-playbook site.yml -t locust_run -e "run_type=backup"`

### 测试模式
- **独立模式**: 用于较小负载的单节点测试
- **分布式模式**: 多节点协调测试（计划功能）

## 使用方法

### 显示内置使用说明

剧本包含可以使用以下命令显示的全面内置使用说明：

```bash
ansible-playbook site.yml --tags usage
```

此命令将显示：
- **可用顶级标签**: `play_prepare`、`play_generator`、`locust_run`、`deploy_all`
- **Locust 任务执行详情**: 如何使用带有 `run_type` 变量的 `03-locust` 角色
- **必需变量**: `run_type`（start/stop/backup）和场景定义文件
- **可选变量**: `standalone.sync_data`、`standalone.build_image`、`standalone.debug`
- **内置示例**: 不同场景的即用命令示例

### 基础设施部署

在执行任何命令之前导航到 performance 目录：

```bash
cd projects/performance
```

#### Complete Deployment

Deploy all performance services (preparation + generator):

```bash
# All-in-one command: deploy all components
ansible-playbook site.yml -t deploy_all
```

#### Targeted Deployment

Deploy specific components individually:

```bash
# Infrastructure preparation only
ansible-playbook site.yml -t play_prepare

# Generator services only
ansible-playbook site.yml -t play_generator
```

### Locust Load Testing

#### Configuration Priority

Variable priority (highest to lowest):
1. Command-line specified variables (highest priority)
2. Extra-vars files (`extra-vars/locust/*.yml`)
3. Role default variables (`roles/03-locust/vars/main.yml`)

#### Standalone Mode Execution

Execute Locust in standalone mode with specific configuration:

```bash
# Using specific extra-vars configuration file
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"

# Alternative: Direct tag specification
ansible-playbook site.yml -t global,known_hosts,sync_repo,task_build_locust,standalone -e "@extra-vars/locust/locust-standalone-pres.yml"
```

#### Available Scenarios

- **Enquete Scenario**: `extra-vars/locust/locust-standalone-enquete.yml`
- **Presentation Scenario**: `extra-vars/locust/locust-standalone-pres.yml`

### Examples

#### Complete Deployment Examples
```bash
# Deploy complete performance testing environment
ansible-playbook site.yml -t deploy_all

# Deploy with specific scenario configuration
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-enquete.yml"
```

#### Phase-by-Phase Deployment Examples
```bash
# Phase 1: Prepare infrastructure only
ansible-playbook site.yml -t play_prepare

# Phase 2: Deploy generator services
ansible-playbook site.yml -t play_generator

# Phase 3: Execute Locust tests
ansible-playbook site.yml -t locust_run -e "@extra-vars/locust/locust-standalone-enquete.yml"
```

#### Locust Testing Examples
```bash
# Start a test with enquete scenario
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start"

# Start a test with presentation scenario and debugging
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=start" -e "standalone.debug=true"

# Start a test with data sync and image rebuild
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start" -e "standalone.sync_data=true" -e "standalone.build_image=true"

# Stop a running test
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=stop"

# Backup test results
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-pres.yml" -e "run_type=backup"
```

#### Advanced Configuration Examples
```bash
# Override default variables from command line
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-enquete.yml" -e "run_type=start" -e "standalone.scenario_data.relative_exec_scene_dir=scenarios/CustomTest/*"

# Display usage instructions
ansible-playbook site.yml --tags usage
```

## Built-in Usage Instructions Details

When you run `ansible-playbook site.yml --tags usage`, the system displays the following comprehensive guide:

### Available Top-Level Tags
- **`play_prepare`**: Installs required dependencies on the generator
- **`play_generator`**: Deploys and configures the test generator services
- **`locust_run`**: Executes Locust tasks (start, stop, backup)
- **`deploy_all`**: Runs both play_prepare and play_generator

### Locust Task Execution
The `03-locust` role is controlled by the `run_type` variable:

#### Required Variables
- **`run_type`**: Defines the action. Can be 'start', 'stop', or 'backup'
- **`extra-vars`**: You must provide a scenario definition file from `extra-vars/locust/`

#### Optional Boolean Variables (for 'start' run_type)
- **`standalone.sync_data`**: (default: false) Set to true to sync test data from the repo
- **`standalone.build_image`**: (default: false) Set to true to rebuild the locust docker image
- **`standalone.debug`**: (default: false) Set to true to display live container logs

#### Available Tags for Locust Tasks
- **`standalone`** / **`locust_standalone`**: To run locust in standalone mode

### Built-in Examples from Usage Instructions
```bash
# Run all preparation and deployment steps
ansible-playbook site.yml -t deploy_all -e "@extra-vars/locust/locust-standalone-sde.yml"

# Using the extra-vars file directly in the command line to start a standalone locust test
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml"

# Start a standalone locust test with debugging enabled
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=start" -e "standalone.debug=true"

# Stop a running test
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=stop"

# Backup test results
ansible-playbook site.yml -t locust_run,standalone -e "@extra-vars/locust/locust-standalone-sde.yml" -e "run_type=backup"
```

## Configuration

### Variables

Key configuration variables are defined in:
- `roles/Dependencies/SetFacts/vars/main.yml`: Global performance testing variables
- `roles/03-locust/vars/main.yml`: Default Locust configuration
- `extra-vars/locust/*.yml`: Scenario-specific overrides

### Inventory Groups

Configure your inventory file with appropriate generator groups:

```ini
[generator]
generator1.performance.local
generator2.performance.local

[locust]
locust1.performance.local
locust2.performance.local
```

### Extra Variables Files

Create custom scenario configurations in `extra-vars/locust/`:

```yaml
# Example: custom-scenario.yml
locust:
  target_host: "https://your-target-server.com"
  users: 100
  spawn_rate: 10
  run_time: "5m"
```

## Roles Description

### 01-prepare
- System updates and upgrades
- Docker installation and dependencies
- Pip3 installation
- Basic infrastructure preparation

### 02-generator
- Generator service deployment
- Conda environment setup
- Key management
- Service configuration

### 03-locust
- Locust framework installation
- Test scenario configuration
- Load testing execution
- Results collection

## Load Testing Best Practices

1. **Resource Planning**: Ensure generator servers have sufficient CPU and memory
2. **Network Considerations**: Monitor network bandwidth during testing
3. **Baseline Testing**: Start with small loads and gradually increase
4. **Monitoring**: Monitor both generator and target system metrics
5. **Test Duration**: Plan appropriate test durations for meaningful results
6. **Environment Isolation**: Use dedicated testing environments

## Monitoring and Logging

### Test Execution Logs

- Ansible execution logs: `./ansible.log`
- Locust test results: Check generator servers for detailed results
- System metrics: Monitor CPU, memory, and network usage

### Performance Metrics

Monitor the following during test execution:
- Response times
- Throughput (requests per second)
- Error rates
- Resource utilization on target systems

## Troubleshooting

### Common Issues

1. **Generator Connection Issues**: Verify SSH keys and inventory configuration
2. **Conda Environment Errors**: Check Python version compatibility
3. **Locust Startup Failures**: Verify target host accessibility
4. **Resource Constraints**: Monitor system resources on generator nodes

### Debug Mode

Run with verbose output for troubleshooting:

```bash
ansible-playbook site.yml -t deploy_all -vvv
```

### Validation Commands

```bash
# Check generator status
ansible generator -m ping

# Verify Locust installation
ansible generator -m shell -a "locust --version"

# Check conda environment
ansible generator -m shell -a "conda info --envs"
```

## Security Considerations

- Secure SSH keys and limit access to generator servers
- Use isolated networks for performance testing
- Avoid testing production systems without proper authorization
- Monitor and log all testing activities
- Implement proper cleanup procedures after testing

## Scaling Performance Tests

### Distributed Testing

For large-scale testing:
1. Deploy multiple generator nodes
2. Configure Locust in distributed mode
3. Use load balancers for result aggregation
4. Monitor resource usage across all nodes

### Resource Optimization

- Optimize test scripts for efficiency
- Use appropriate spawn rates
- Monitor and tune JVM/Python settings
- Consider using dedicated testing hardware

## Contributing

When adding new functionality:

1. Follow existing role structure and naming conventions
2. Add comprehensive documentation for new scenarios
3. Test thoroughly with different load patterns
4. Update configuration examples
5. Consider backward compatibility with existing scenarios

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Ansible and Locust documentation
3. Validate configuration files and inventory
4. Monitor system logs for detailed error information