---
galaxy_info:
  role_name: locust
  author: Performance Testing Team
  description: Executes Locust performance tests and manages test scenarios
  company: NeoX Inc
  license: MIT
  min_ansible_version: "2.9"
  platforms:
    - name: Ubuntu
      versions:
        - bionic
        - focal
        - jammy
  galaxy_tags:
    - performance
    - testing
    - locust
    - loadtesting

allow_duplicates: false
dependencies:
  - role: Dep.SetFacts
  - role: Dep.RepoOps
