---
- name: Include SetFacts role to ensure PERF variable is set
  ansible.builtin.import_role:
    name: Dep.SetFacts
  tags:
    - always

- name: Include RepoOps role for repository synchronization
  ansible.builtin.import_role:
    name: Dep.RepoOps
    tasks_from: task-sync-repo
  tags:
    - always

- name: Include 02-generator role for building locust
  ansible.builtin.import_role:
    name: 02-generator
    tasks_from: task-build-locust
  tags:
    - always

- name: Execute Locust standalone tasks
  ansible.builtin.import_tasks: locust-standalone.yml
  tags:
    - standalone
    - locust_standalone
# TODO: 分布式模式
# - name: Execute Locust distributed tasks
#   ansible.builtin.import_tasks: locust-distributed.yml
#   tags:
#     - distributed
#     - locust_distributed
