---
- name: "Standalone Mode: Start Locust load test"
  when: "'start' in run_type | lower"
  block:
    - name: Prepare project data synchronization
      when: standalone.sync_data | bool
      block:
        - name: Sync project files to target node
          ansible.builtin.copy:
            src: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}/{{ standalone.scenario_data.relative_src_dir }}/"
            dest: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/"
            remote_src: true
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: preserve

        - name: Set project environment directory path
          ansible.builtin.set_fact:
            project_env_dir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ standalone.scenario_data.relative_dest_dir }}/env"

        - name: Copy docker-compose files to target directory
          ansible.builtin.copy:
            src: "{{ project_env_dir }}/{{ item }}"
            dest: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/{{ item }}"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: "0664"
            force: true
            remote_src: true
          loop: "{{ standalone.docker_compose_files | default([]) }}"

    - name: Build Locust Docker image if required
      when: standalone.build_image | bool
      block:
        - name: Ensure BuildImage.sh is executable
          ansible.builtin.file:
            path: "{{ project_env_dir }}/BuildImage.sh"
            owner: "{{ ansible_user }}"
            group: "{{ ansible_user }}"
            mode: "0755"

        - name: Build Locust Docker image
          ansible.builtin.command: "bash BuildImage.sh"
          args:
            chdir: "{{ project_env_dir }}"
          register: build_result
          changed_when: "'Successfully built' in build_result.stdout"

    - name: Start Locust containers
      ansible.builtin.command: "docker-compose up -d"
      args:
        chdir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
      register: compose_result
      changed_when: "'done' in compose_result.stdout or 'up-to-date' in compose_result.stdout"

    - name: Verify Locust container is running
      ansible.builtin.command: "docker ps -q -f name={{ PERF.locust.scenarios.container_name }}"
      register: container_check
      changed_when: false
      failed_when: container_check.stdout == ""
      retries: 3
      delay: 5

    - name: Display debug information
      when: standalone.debug | bool
      block:
        - name: Get current timestamp
          ansible.builtin.command: date +%Y-%m-%d_%H-%M-%S
          register: current_timestamp
          changed_when: false
          delegate_to: localhost

        - name: Set log file paths
          ansible.builtin.set_fact:
            locust_log_dir: >-
              {{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/
              {{ standalone.scenario_data.relative_dest_dir }}/
              {{ standalone.scenario_data.relative_exec_scene_dir }}
            locust_log_file: "run.log"
            internal_log_file: "internal_{{ current_timestamp.stdout }}.log"

        - name: Check container status
          ansible.builtin.command: "docker inspect -f '{% raw %}{{.State.Status}}{% endraw %}' {{ PERF.locust.scenarios.container_name }}"
          register: container_status
          ignore_errors: true
          changed_when: false

        - name: Display container status
          ansible.builtin.debug:
            msg: "Container status: {{ container_status.stdout | default('Container not found') }}"

        - name: Display recent container logs
          ansible.builtin.command: "docker logs --tail 50 {{ PERF.locust.scenarios.container_name }}"
          register: container_logs
          ignore_errors: true
          changed_when: false

        - name: Show container logs
          ansible.builtin.debug:
            msg: "{{ container_logs.stdout_lines[-20:] if container_logs.stdout_lines else ['No logs available'] }}"

        - name: Display log file content if available
          ansible.builtin.command: "tail -n 50 {{ locust_log_dir }}/{{ locust_log_file }}"
          register: log_content
          ignore_errors: true
          changed_when: false

        - name: Show log file content
          ansible.builtin.debug:
            msg: "{{ log_content.stdout_lines if log_content.stdout_lines else ['Log file not available'] }}"

- name: "Standalone Mode: Stop Locust load test"
  when: "'stop' in run_type | lower"
  block:
    - name: Stop Locust containers
      ansible.builtin.command: "docker-compose down"
      args:
        chdir: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
      register: compose_down_result
      changed_when: "'Stopping' in compose_down_result.stdout or 'Removing' in compose_down_result.stdout"
      failed_when: false

    - name: Display stop operation result
      ansible.builtin.debug:
        msg: "Locust containers stopped: {{ compose_down_result.rc == 0 }}"

    - name: Verify containers are stopped
      ansible.builtin.command: "docker ps -q -f name={{ PERF.locust.scenarios.container_name }}"
      register: container_stop_check
      changed_when: false
      failed_when: container_stop_check.stdout != ""
      retries: 3
      delay: 2

- name: "Standalone Mode: Backup Locust results"
  when: "'backup' in run_type | lower"
  block:
    - name: Set backup directory paths
      ansible.builtin.set_fact:
        backup_src_dir: >-
          {{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/
          {{ standalone.scenario_data.relative_dest_dir }}/
          {{ standalone.scenario_data.relative_exec_scene_dir }}/
          {{ standalone.backup.relative_src_dir }}
        backup_dest_dir: >-
          {{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}/
          {{ standalone.scenario_data.relative_dest_dir }}/
          {{ standalone.scenario_data.relative_exec_scene_dir }}/
          {{ standalone.backup.relative_tar_dir }}

    - name: Ensure backup directory exists
      ansible.builtin.file:
        path: "{{ backup_dest_dir }}"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0755"

    - name: Generate timestamp for backup
      ansible.builtin.command: date +%Y_%m_%d-%H_%M_%S
      register: timestamp
      changed_when: false
      delegate_to: localhost

    - name: Create timestamped backup directory
      ansible.builtin.file:
        path: "{{ backup_dest_dir }}/Result-{{ timestamp.stdout }}"
        state: directory
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0755"

    - name: Copy results to backup directory
      ansible.builtin.copy:
        src: "{{ backup_src_dir }}/"
        dest: "{{ backup_dest_dir }}/Result-{{ timestamp.stdout }}/"
        owner: "{{ ansible_user }}"
        group: "{{ ansible_user }}"
        mode: "0644"
        remote_src: true
      register: copy_result

    - name: Verify backup completion
      ansible.builtin.debug:
        msg: "Backup completed successfully to {{ backup_dest_dir }}/Result-{{ timestamp.stdout }}"
      when: copy_result is success
