---
- name: Convert GPG key to binary format
  ansible.builtin.command:
    cmd: gpg --dearmor --output /usr/share/keyrings/docker-archive-keyring.gpg /usr/share/keyrings/docker-archive-keyring.gpg.asc
  args:
    creates: /usr/share/keyrings/docker-archive-keyring.gpg

- name: Remove temporary ASCII GPG key file
  ansible.builtin.file:
    path: /usr/share/keyrings/docker-archive-keyring.gpg.asc
    state: absent

- name: Reset SSH connection to apply group changes
  ansible.builtin.meta: reset_connection
