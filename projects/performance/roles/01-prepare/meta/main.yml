---
galaxy_info:
  role_name: prepare
  author: Performance Testing Team
  description: Prepares infrastructure and installs dependencies for performance testing
  company: NeoX Inc
  license: MIT
  min_ansible_version: "2.9"
  platforms:
    - name: Ubuntu
      versions:
        - bionic
        - focal
        - jammy
  galaxy_tags:
    - performance
    - testing
    - infrastructure
    - docker
    - preparation

allow_duplicates: false
dependencies:
  - role: Dep.SetFacts
  - role: Dep.SshConnection
