---
- name: Update and upgrade apt packages
  ansible.builtin.apt:
    update_cache: true
    upgrade: dist
    cache_valid_time: 3600
  register: apt_result
  become: true

- name: Verify apt operation completed successfully
  ansible.builtin.assert:
    that:
      - not apt_result.failed
      - apt_result.changed is defined
    fail_msg: "APT update/upgrade failed: {{ apt_result.msg | default('Unknown error') }}"
    success_msg: "APT update/upgrade completed successfully"
