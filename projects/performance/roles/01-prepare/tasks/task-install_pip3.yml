---
- name: Install Python3 pip package
  ansible.builtin.apt:
    name: python3-pip
    state: present
    update_cache: true
    cache_valid_time: 3600
  register: pip_install_result
  become: true

- name: Verify pip3 installation completed successfully
  ansible.builtin.assert:
    that:
      - not pip_install_result.failed
    fail_msg: "Python3 pip installation failed: {{ pip_install_result.msg | default('Unknown error') }}"
    success_msg: "Python3 pip installed successfully"

- name: Verify pip3 is available
  ansible.builtin.command: pip3 --version
  register: pip_version_check
  changed_when: false
  failed_when: pip_version_check.rc != 0
