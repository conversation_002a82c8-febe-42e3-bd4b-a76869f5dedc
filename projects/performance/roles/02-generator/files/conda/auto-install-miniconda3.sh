#!/usr/bin/bash

set -e

# wget "https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh" -O ~/miniconda3.sh
wget "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh" -O ~/miniconda3.sh
bash ~/miniconda3.sh -b -p "${HOME}"/miniconda3
~/miniconda3/bin/conda init "$(echo "${SHELL}" | awk -F '/' '{print $NF}')"
#echo 'Successfully installed miniconda3...'

#echo -n 'Conda version: '
#~/miniconda3/bin/conda --version

~/miniconda3/bin/conda config --set auto_activate_base false
~/miniconda3/bin/conda config --set always_yes true
~/miniconda3/bin/conda config --set show_channel_urls yes
# ~/miniconda3/bin/conda config --add channels https://mirrors.aliyun.com/anaconda/cloud/bioconda/
# ~/miniconda3/bin/conda config --add channels https://mirrors.aliyun.com/anaconda/cloud/conda-forge/
# ~/miniconda3/bin/conda config --add channels https://mirrors.aliyun.com/anaconda/pkgs/main/

#echo -e '\n'
#exec bash
