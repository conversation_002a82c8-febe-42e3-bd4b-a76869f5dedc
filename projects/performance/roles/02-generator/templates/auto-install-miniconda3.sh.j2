#!/usr/bin/bash

set -e

# wget "https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/Miniconda3-latest-Linux-x86_64.sh" -O {{ PERF.home_path }}/miniconda3.sh
wget "https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh" -O {{ PERF.home_path }}/miniconda3.sh
bash {{ PERF.home_path }}/miniconda3.sh -b -p {{ PERF.home_path }}/miniconda3
{{ PERF.home_path }}/miniconda3/bin/conda init "$(echo "${SHELL}" | awk -F '/' '{print $NF}')"
#echo 'Successfully installed miniconda3...'

#echo -n 'Conda version: '
#~/miniconda3/bin/conda --version

{{ PERF.home_path }}/miniconda3/bin/conda config --set auto_activate_base false
{{ PERF.home_path }}/miniconda3/bin/conda config --set always_yes true
{{ PERF.home_path }}/miniconda3/bin/conda config --set show_channel_urls yes
# {{ PERF.home_path }}/miniconda3/bin/conda config --add channels https://mirrors.aliyun.com/anaconda/cloud/bioconda/
# {{ PERF.home_path }}/miniconda3/bin/conda config --add channels https://mirrors.aliyun.com/anaconda/cloud/conda-forge/
# {{ PERF.home_path }}/miniconda3/bin/conda config --add channels https://mirrors.aliyun.com/anaconda/pkgs/main/

#echo -e '\n'
#exec bash
