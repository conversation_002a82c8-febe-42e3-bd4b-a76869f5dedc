---
- name: Check if locustio/locust image exists
  ansible.builtin.command: >
    docker images locustio/locust:{{ PERF.locust.conda.locust_version }}
    --format '{% raw %}{{.Repository}}:{{.Tag}}{% endraw %}'
  register: locustio_image_check
  changed_when: false
  failed_when: false

- name: Pull locustio/locust image if not present
  ansible.builtin.command: "docker pull locustio/locust:{{ PERF.locust.conda.locust_version }}"
  register: pull_result
  when: locustio_image_check.stdout == ""
  retries: 3
  delay: 5
  changed_when: "'Downloaded newer image' in pull_result.stdout"

- name: Check if neox-locust image exists
  ansible.builtin.command: >
    docker images --filter "reference=neox-locust:{{ PERF.locust.image['neox-locust'] }}"
    --format '{% raw %}{{.Repository}}:{{.Tag}}{% endraw %}'
  register: neox_image_check
  changed_when: false
  failed_when: false

- name: Build neox-locust image if not present
  ansible.builtin.command:
    chdir: "{{ PERF.home_path }}/{{ PERF.repo.perf.repo_root_dir }}/{{ PERF.repo.perf.repo_name }}/{{ PERF.locust.image.relative_dir_path_in_repo }}"
    cmd: "bash BuildImage.sh"
  when:
    - neox_image_check.stdout == ""
    - pull_result is success or pull_result is skipped
  register: build_result
  changed_when: "'Successfully built' in build_result.stdout"

- name: Create locust docker compose directory
  ansible.builtin.file:
    path: "{{ PERF.home_path }}/{{ PERF.locust.scenarios.relative_root_dir }}"
    state: directory
    mode: "0755"
    owner: "{{ ansible_user }}"
    group: "{{ ansible_user }}"
