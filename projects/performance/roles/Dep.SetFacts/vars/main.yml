---
# Performance testing configuration
perf:
  # System paths
  home_path: "{{ ansible_env.HOME }}"
  dc_version: "2.33.0"

  # Repository configuration
  repo:
    ansible:
      semaphore_home_path: "{{ ansible_env.HOME }}"
      repo_root_dir: "docker-compose/semaphore/tmp"
      repo_name: "ansible"
    perf:
      ssh_key_name: "neox-bitbucket-testing"
      address: "*****************:neoxinc/testing.git"
      repo_root_dir: "bitbucket"
      repo_name: "testing"
      target_branch: "main"

  # Locust configuration
  locust:
    conda:
      relative_conda_path: "miniconda3/bin/conda"
      python_version: "3.13"
      locust_version: "2.37.10"
    image:
      neox-locust: "2025062701"
      relative_dir_path_in_repo: "Performance/Locust/env"
    requirements:
      relative_dir_path_in_repo: "Performance/Locust/env/requirements"
    scenarios:
      relative_root_dir: "docker-compose/locust"
      container_name: "locust"
