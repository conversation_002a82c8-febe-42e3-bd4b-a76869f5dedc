---
# start | stop | backup
run_type: start

standalone:
  sync_data: true
  build_image: true
  debug: true
  backup:
    relative_src_dir: result
    relative_tar_dir: backup
  scenario_data:
    relative_src_dir: Performance/Locust
    relative_dest_dir: NeoX
    # 执行场景目录，不同场景下一般需要改动的只有这里 "scenarios/" 后面的路径
    relative_exec_scene_dir: scenarios/Recognition/Engine
  docker_compose_files:
    - ".env"
    - "docker-compose.yml"
