services:
    semaphore_db:
        restart: unless-stopped
        ports:
            - 5432:5432
        user: "0:0"
        image: postgres:16
        environment:
            POSTGRES_DB: ${POSTGRES_DB}
            POSTGRES_USER: ${POSTGRES_USER}
            POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
        volumes:
            - /home/<USER>/docker-compose/postgres/data:/var/lib/postgresql/data
        networks:
            - neox_auto_ops
    semaphore:
        restart: unless-stopped
        ports:
            - 3333:3000
        user: "1000:1000"
        depends_on:
            - semaphore_db
        image: semaphoreui/semaphore:v2.14.10
        environment:
            SEMAPHORE_DB_DIALECT: postgres
            SEMAPHORE_DB_HOST: semaphore_db
            SEMAPHORE_DB_NAME: ${POSTGRES_DB}
            SEMAPHORE_DB_USER: ${POSTGRES_USER}
            SEMAPHORE_DB_PASS: ${POSTGRES_PASSWORD}
            SEMAPHORE_ADMIN: ${SEMAPHORE_ADMIN}
            SEMAPHORE_ADMIN_PASSWORD: ${SEMAPHORE_ADMIN_PASSWORD}
            SEMAPHORE_ADMIN_NAME: ${SEMAPHORE_ADMIN_NAME}
            SEMAPHORE_ADMIN_EMAIL: ${SEMAPHORE_ADMIN_EMAIL}
            SEMAPHORE_ACCESS_KEY_ENCRYPTION: "${SEMAPHORE_ACCESS_KEY_ENCRYPTION}"
            # SEMAPHORE_USE_REMOTE_RUNNER: "True"
            # SEMAPHORE_RUNNER_REGISTRATION_TOKEN: "${SEMAPHORE_RUNNER_REGISTRATION_TOKEN}"
            TZ: Asia/Tokyo
        volumes:
            - /home/<USER>/docker-compose/semaphore/data:/var/lib/semaphore
            - /home/<USER>/docker-compose/semaphore/config:/etc/semaphore
            - /home/<USER>/docker-compose/semaphore/tmp:/tmp/semaphore
            - /home/<USER>/.ssh:/home/<USER>/.ssh
        networks:
            - neox_auto_ops

networks:
    # sudo docker network create neox_auto_ops
    neox_auto_ops:
        external: true