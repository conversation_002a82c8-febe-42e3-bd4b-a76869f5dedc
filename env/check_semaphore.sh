#!/bin/bash

# 检查是否提供了间隔时间参数
if [ $# -ne 1 ]; then
    echo "用法: $0 <探测间隔秒数>"
    exit 1
fi

INTERVAL=$1
ROOT_PATH="${HOME}/docker-compose/semaphore/bin"
# LOG_FILE="${ROOT_PATH}/check_semaphore.log"
LOG_PATH="/mnt/efs/production/devops/logs"
LOG_FILE="${LOG_PATH}/check_semaphore.log"

# 验证间隔时间是否为正整数
if ! [[ "$INTERVAL" =~ ^[0-9]+$ ]] || [ "$INTERVAL" -le 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.ERROR: 间隔时间必须是正整数" | tee -a "$LOG_FILE"
    exit 1
fi

# 确保日志文件存在且有写入权限
touch "$LOG_FILE" 2>/dev/null || {
    echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.ERROR: 无法创建或写入日志文件 $LOG_FILE" | tee -a "$LOG_FILE"
    exit 1
}

# 记录脚本启动时间
echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.INFO: 脚本启动，探测间隔为 ${INTERVAL} 秒" >> "$LOG_FILE"

# 后台循环探测
while true; do
    # 记录每次探测的时间
    echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.INFO: 开始探测 semaphore 进程" >> "$LOG_FILE"
    
    # 检查 semaphore 进程是否运行
    if ! ps -ef | grep bin/semaphore | grep -v grep > /dev/null; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.WARNING: 未找到 semaphore 进程，执行 semaphore.sh" >> "$LOG_FILE"
        # 执行 semaphore.sh 并记录执行结果
        if bash ${ROOT_PATH}/semaphore.sh >> "$LOG_FILE" 2>&1; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.INFO: semaphore.sh 执行成功" >> "$LOG_FILE"
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.ERROR: semaphore.sh 执行失败" >> "$LOG_FILE"
        fi
    else
        echo "$(date '+%Y-%m-%d %H:%M:%S') check_semaphore.INFO: semaphore 进程正在运行，无需执行 semaphore.sh" >> "$LOG_FILE"
    fi
    
    # 等待指定的间隔时间
    sleep "$INTERVAL"
done &