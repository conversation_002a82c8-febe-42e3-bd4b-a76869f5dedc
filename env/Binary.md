# How to setup binary

[Binary Download](https://semaphoreui.com/install/binary/2_14/install)

```bash
cat << EOF > ~/docker-compose/semaphore/bin/config.json
{
  "postgres": {
    "host": "localhost",
    "name": "semaphore",
    "user": "semaphore",
    "pass": "NeoX@Semaphore",
    "options": {
      "sslmode": "disable"
    }
  },
  "dialect": "postgres",
  "port": "3333",
  "tmp_path": "/home/<USER>/docker-compose/semaphore/tmp",
  "cookie_hash": "HJf2gE2pefE6Nh6WiYk2wSPYpEpUZhQcBnLlalzHvjA=",
  "cookie_encryption": "1YNy640qCt+eDQ/WTrvzDYYRc2b3QOSuo4RtWelRc0c=",
  "access_key_encryption": "RkUCENdMlfiKsWVkwLChi8XPsluQ1sHC2BJIEjt3lNU="
}
EOF


semaphore user add --admin \
  --login admin \
  --password Admin@Semaphore \
  --name "Admin" \
  --email "<EMAIL>" \
  --config ./config.json


semaphore server --config ./config.json
```

**or**

```bash
export SEMAPHORE_DB_DIALECT=postgres
export SEMAPHORE_DB_HOST=localhost
export SEMAPHORE_DB_NAME=semaphore
export SEMAPHORE_DB_USER=semaphore
export SEMAPHORE_DB_PASS=NeoX@Semaphore
export SEMAPHORE_DB_OPTIONS='{"sslmode": "disable"}'
export SEMAPHORE_PORT=3333
export SEMAPHORE_TMP_PATH=/home/<USER>/docker-compose/semaphore/tmp
export SEMAPHORE_COOKIE_HASH=HJf2gE2pefE6Nh6WiYk2wSPYpEpUZhQcBnLlalzHvjA=
export SEMAPHORE_COOKIE_ENCRYPTION=1YNy640qCt+eDQ/WTrvzDYYRc2b3QOSuo4RtWelRc0c=
export SEMAPHORE_ACCESS_KEY_ENCRYPTION=RkUCENdMlfiKsWVkwLChi8XPsluQ1sHC2BJIEjt3lNU=


semaphore user add --admin \
  --login admin \
  --password Admin@Semaphore \
  --name "Admin" \
  --email "<EMAIL>" \
  --no-config


semaphore server --no-config
```

## Prepare Start Semaphore Shell

```bash
cat << EOF > ~/docker-compose/semaphore/bin/semaphore.sh
#!/bin/bash

ROOT_PATH="${HOME}/docker-compose/semaphore/bin"
nohup ${ROOT_PATH}/semaphore server --config ${ROOT_PATH}/config.json > ${ROOT_PATH}/semaphore.log 2>&1 &
EOF


chmod +x ~/docker-compose/semaphore/bin/semaphore.sh
```

Create check_semaphore.sh script:
👉 **[check_semaphore.sh](./check_semaphore.sh)**

Edit .bashrc file to add alias for check semaphore and then source it:

```bash
vi ~/.bashrc

# add following lines to the end of .bashrc file
alias checksemaphore='nohup bash ${HOME}/docker-compose/semaphore/bin/check_semaphore.sh 60 > /dev/null 2>&1 &'

# source .bashrc file after saving changes
source ~/.bashrc
```

Run checksemaphore alias to start the script:

```bash
checksemaphore
```

Then config cron to start semaphore shell:

```bash
crontab -e
```

Add the following line to the end of the file:

```bash
@reboot /bin/bash /home/<USER>/docker-compose/semaphore/bin/check_semaphore.sh
```
