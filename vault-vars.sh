#!/bin/bash
: << !
    @illustration: Encrypt ansible vars file in each role
    @exec: bash vault-vars.sh \$1 \$2
        '$1':
            help - help docs
            valid - valid expect whether installed
            "<project dir name>" - the sibling path with vault_vars.sh
        '$2':
            encrypt - encrypt file by ansible-vault
            decrypt - decrypt file by ansible-vault
            refresh - refresh vault password file: VPF
    @author: KunoLu
    @time: 2025/02/27
!

PROJECT_PATH="./projects/$1"
ROLES_PATH="${PROJECT_PATH}/roles"
VARS_FILE_SUB_PATH='vars/main.yml'
VAULT_PASS_FILE_PATH=${PROJECT_PATH}/vault_pass.txt

RED_COLOR='\e[31m'
GREEN_COLOR='\e[32m'
YELLOW_COLOR='\e[33m'
SUFFIX_COLOR_TAG='\e[0m'

function valid_expect {
    expect_version=$(expect -v)
    if [ $? -ne 0 ]; then
        os=$(uname -a)
        if [[ ${os} =~ [Uu]buntu ]];then
            update=$(sudo apt-get update)
            install=$(sudo apt-get install expect)
            echo -e "${GREEN_COLOR}Expect installed on Ubuntu!${SUFFIX_COLOR_TAG}"
        elif [[ ${os} =~ [Cc]entos ]];then
            update=$(sudo yum makecache)
            install=$(sudo yum -y install expect)
            echo -e "${GREEN_COLOR}Expect installed on Centos!${SUFFIX_COLOR_TAG}"
        elif [[ ${os} =~ [Dd]arwin ]];then
            update=$(brew update)
            install=$(brew install expect)
            echo -e "${GREEN_COLOR}Expect installed on MacOS!${SUFFIX_COLOR_TAG}"
        else
            echo -e "${RED_COLOR}Your OS is: [ ${os} ]. Pls install expect.${SUFFIX_COLOR_TAG}"
        fi
    else
        echo -e "${GREEN_COLOR}Expect installed! The version is: [ ${expect_version} ].${SUFFIX_COLOR_TAG}"
    fi
}

[ "$1" = "help" ] && echo -e "${GREEN_COLOR}EXEC CMD: bash vault-vars.sh \$1 \$2\nPARAM:\n    \$1: help | valid | <project dir name>\n    \$2: encrypt | decrypt | refresh${SUFFIX_COLOR_TAG}" && exit

[ "$1" = "valid" ] && valid_expect && exit

ROLES=($(ls ${ROLES_PATH}))
PASSWD=$(cat ${VAULT_PASS_FILE_PATH} | base64)

echo -n ${PASSWD} > ${PROJECT_PATH}/VPF

for role in ${ROLES[*]}
do
    vars_file_path="${ROLES_PATH}/${role}/${VARS_FILE_SUB_PATH}"
    if [ -f "${vars_file_path}" ];then
        case $2 in
            encrypt)
                expect <<EOF
                set timeout 10
                spawn ansible-vault $2 ${vars_file_path}
                expect {
                    "New Vault password" { send "${PASSWD}\n";exp_continue }
                    "Confirm New Vault password" { send "${PASSWD}\n" }
                }
                expect eof
EOF
                echo -e "${GREEN_COLOR}Exec: [ ansible-vault $2 ${vars_file_path} ]${SUFFIX_COLOR_TAG}"
                ;;
            decrypt)
                expect <<EOF
                set timeout 10
                spawn ansible-vault $2 ${vars_file_path}
                expect {
                    "Vault password" { send "${PASSWD}\n" }
                }
                expect eof
EOF
                echo -e "${GREEN_COLOR}Exec: [ ansible-vault $2 ${vars_file_path} ]${SUFFIX_COLOR_TAG}"
                ;;
            refresh)
                echo -e "${GREEN_COLOR}Encrypt password has been written into VPF.${SUFFIX_COLOR_TAG}"
                break
                ;;
            *)
                echo -e "${YELLOW_COLOR}Your param is: { \$1: $1, \$2: $2 }. Pls use: { \$1: <project-dir-name>, \$2: encrypt / decrypt / refresh }${SUFFIX_COLOR_TAG}"
                break
        esac
    else
        echo -e "${RED_COLOR}[ ${vars_file_path} ] is not exists.${SUFFIX_COLOR_TAG}"
    fi
done